package com.wisematch.common.utils;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.common.model.IUserTime;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * mybatis解决空指针异常
 * 简化需要返回对象但是没返回的判空操作
 * *
 */

public class MybatisUtils {

    public static <T> List<T> listByIds(IService<T> service,
                                        Collection<? extends Serializable> idList) {
        if (!CollectionUtils.isEmpty(idList)) {
            return service.listByIds(idList);
        }
        return new ArrayList<T>();
    }

    public static T getById(IService<T> service, Serializable id) {
        T entity = new T();
        T selectOne = service.getById(id);
        if (null != selectOne) {
            entity = selectOne;
        }
        return entity;
    }

    public static T selectOne(IService<T> service, @Param("ew") Wrapper<T> queryWrapper) {
        T entity = new T();
        T selectOne = service.getOne(queryWrapper);
        if (null != selectOne) {
            entity = selectOne;
        }
        return entity;
    }
    /**
     * 查询指定用户最新的一条记录（基于 IUserTimeEntity 接口）
     */
    public static <T extends IUserTime<V>, V> T getLatestOneByUserId(
            IService<T> service,
            V userId
    ) {
        if (userId == null) return null;

        LambdaQueryWrapper<T> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(T::getUserId, userId)
                .orderByDesc(T::getCreateTime)
                .last("limit 1");

        return service.getOne(wrapper);
    }


}
