package com.wisematch.common.exception;

import com.wisematch.common.utils.HttpContextUtils;
import com.wisematch.common.utils.R;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.nio.file.AccessDeniedException;


/**
 * 异常处理器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年10月27日 下午10:16:19
 */
@Slf4j
@RestControllerAdvice
public class RRExceptionHandler extends R {
    /**
     * 自定义异常
     */
    @ExceptionHandler(RRException.class)
    public R handleRRException(RRException e) {
        log.error("RRE异常:", e);
        return R.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(DuplicateKeyException.class)
    public R handleDuplicateKeyException(DuplicateKeyException e) {
        log.error("请求地址'{}',发生数据库异常.", e);
        return R.error(RRExceptionEnum.DUPLICATE_PRIMARY_KEY);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public R handleAccessDeniedException(AccessDeniedException e,
                                         HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址{},权限校验失败{}", requestURI, e.getMessage());
        return R.error(RRExceptionEnum.ADMIN_ACCESS_FORBIDDEN);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
                                                 HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址{},不支持{}请求", requestURI, e.getMethod());
        return R.error(RRExceptionEnum.ADMIN_ACCESS_FORBIDDEN);
    }

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public R handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e) {
        log.error("请求方式错误:", e);
        return R.error(RRExceptionEnum.FILE_UPLOAD_FAIL);
    }

    @ExceptionHandler(TypeMismatchException.class)
    public R handleTypeMismatchException(TypeMismatchException e) {
        log.error("参数错误:", e);
        return R.error(RRExceptionEnum.JSON_PARSE_ERROR);
    }

    @ExceptionHandler(MultipartException.class)
    public R handleMultipartException(MultipartException e) {
        log.error("文件上传相关异常:", e);
        return R.error(RRExceptionEnum.FILE_UPLOAD_FAIL);
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public R handleNoHandlerFoundException(NoHandlerFoundException e) {
        log.error("请求路径错误:", e);
        return R.error(RRExceptionEnum.ADMIN_ACCESS_FORBIDDEN);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error("参数错误:", e);
        return R.error(RRExceptionEnum.JSON_PARSE_ERROR);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public R handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error("参数JSON解析错误:", e);
        return R.error(RRExceptionEnum.JSON_PARSE_ERROR);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public R handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        log.error("参数JSON解析错误:", e);
        return R.error(RRExceptionEnum.JSON_PARSE_ERROR);
    }

    @ExceptionHandler(NoResourceFoundException.class)
    public R handleNoResourceFoundException(NoResourceFoundException e) {
        log.error("无该资源:", e);
        return R.error(RRExceptionEnum.ADMIN_ACCESS_FORBIDDEN);
    }

    @ExceptionHandler(BadSqlGrammarException.class)
    public R handleBadSqlGrammarException(BadSqlGrammarException e) {
        String contextPath = HttpContextUtils.getHttpServletRequest().getRequestURI();
        log.error("contextPath:{},SQL语法错误:{}", contextPath, e);
        return R.error(RRExceptionEnum.SQL_SYNTAX_ERROR);
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public R handleDataIntegrityViolationException(DataIntegrityViolationException e,
                                                   HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生数据库异常.", requestURI, e);
        return R.error(RRExceptionEnum.DATABASE_ERROR);
    }

    @ExceptionHandler(RuntimeException.class)
    public R handleRuntimeException(RuntimeException e,
                                    HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址{},发生未知运行异常", requestURI, e);
        return R.error(RRExceptionEnum.SERVICE_ERROR);
    }

    @ExceptionHandler(UncategorizedSQLException.class)
    public R handleUncategorizedSqlException(UncategorizedSQLException e,
                                             HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生数据库异常.", requestURI, e);
        return R.error(RRExceptionEnum.DATABASE_ERROR);
    }


    @ExceptionHandler(Exception.class)
    public R handleException(Exception e) {
        log.error("发生异常", e);
        return R.error(e.getMessage());
    }
}