package com.wisematch.modules.login.service;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.ServiceException;
import com.wisematch.common.cache.RedisCache;
import com.wisematch.common.event.LoginLogEvent;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.HttpContextUtils;
import com.wisematch.common.utils.IPUtils;
import com.wisematch.common.utils.SpringContextUtils;
import com.wisematch.modules.chat.enums.AppLoginType;
import com.wisematch.modules.chat.model.AppLoginBody;
import com.wisematch.modules.chat.model.LoginBody;
import com.wisematch.modules.chat.model.UserInfo;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.login.strategy.PhoneParseStrategy;
import com.wisematch.modules.login.strategy.PhoneParseStrategyFactory;
import com.wisematch.modules.security.auth.CommonLoginHandler;
import com.wisematch.modules.security.auth.OnceClickAuthenticationToken;
import com.wisematch.modules.security.auth.PhoneAuthenticationToken;
import com.wisematch.modules.security.context.AuthenticationContextHolder;
import com.wisematch.modules.sms.AliyunSmsMessageService;
import com.wisematch.modules.sms.MessagePayload;
import com.wisematch.modules.sms.SmsVerifyCodeManager;
import com.wisematch.modules.sys.constant.CacheConstants;
import com.wisematch.modules.sys.entity.SysLoginLog;
import com.wisematch.modules.sys.model.LoginUser;
import com.wisematch.modules.sys.service.SysUserService;
import com.wisematch.modules.sys.service.TokenService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version LoginService.java, v0.1 2025-07-01 14:30
 */
@Service
@Slf4j
public class LoginServiceImpl implements LoginService {

    @Autowired
    private SmsVerifyCodeManager smsVerifyCodeManager;
    @Autowired
    private AliyunSmsMessageService aliyunSmsMessageService;
    @Resource
    private ApplicationContext applicationContext;
    @Autowired
    private SysUserService userService;
    @Autowired
    private AiSysConfigService configService;
    @Autowired
    private TokenService tokenService;

    @Autowired
    CommonLoginHandler commonLoginHandler;
    @Autowired
    PhoneParseStrategyFactory strategyFactory;

    public String phoneParse(AppLoginBody loginBody) throws Exception {

        PhoneParseStrategy strategy = strategyFactory.getStrategy(
                AppLoginType.valueOf(loginBody.getChannel())
        );
        // 执行解析（不再需要传递config！）
        return strategy.parsePhone(loginBody.getToken());
    }

    @Override
    public Boolean sendCode(String phone, String ip) {

        // 检查用户
        this.userService.createUser(phone);

        // 发送短信
        String code = smsVerifyCodeManager.generateVerifyCode(phone, ip);
        if (StringUtils.isEmpty(code)) {
            return false;
        }
        // 准备消息体
        JSONObject content = new JSONObject();
        content.put("code", code);
        MessagePayload payload = new MessagePayload()
                .setContent(content.toString())
                .setTarget(phone);
        aliyunSmsMessageService.sendMessage(payload);
        return true;
    }

    @Override
    public UserInfo getUserInfo(String phone) {
        UserInfo userInfo = this.userService.getUserByPhone(phone);
        String token = SecureUtil.md5(String.valueOf(System.currentTimeMillis()));
        userInfo.setToken(token);
        return userInfo;
    }


    @Resource
    private AuthenticationManager authenticationManager;

    /**
     * 登录验证
     *
     * @return 结果
     */
    @Override
    public String userLogin(LoginBody loginBody) {

        //TODO 目前未启用验证码功能
//        String username = loginBody.getUsername();
//        String password = loginBody.getPassword();
        // 验证码校验
//        validateCaptcha(username, loginBody.getCode(), loginBody.getUuid());
        // 登录前置校验
//        loginPreCheck(username, password);

        boolean loginSuccess = false;
        SysLoginLog loginLog = new SysLoginLog();

        // 用户验证
        Authentication authentication;
        try {
            authentication = phoneVerifyCodeLogin(loginBody);
            Object principal = authentication.getPrincipal();
            LoginUser loginUser = (LoginUser) principal;
            loginLog.setUserId(loginUser.getUserId());
            commonLoginHandler.statusVerify(loginUser);
            loginSuccess = true;
            return tokenService.createToken(loginUser);
        }catch (BadCredentialsException e){
            log.error("登录异常",e);
            throw new RRException(RRExceptionEnum.ADMIN_ACCOUNT_ERROR);
        }catch (RRException e){
            log.error("登录异常",e);
            throw new RRException(e.getCode(), e.getMsg());
        } finally {
            loginLog.setOptionIp(IPUtils.getIpAddr(HttpContextUtils.getHttpServletRequest()));
            loginLog.setOptionName(loginSuccess ? "登录成功" : "登录失败");
            loginLog.setOptionTerminal(HttpContextUtils.getHttpServletRequest().getHeader("User-Agent"));
            loginLog.setUsername(loginBody.getUsername());
            loginLog.setOptionTime(new Date());
            SpringContextUtils.publishEvent(new LoginLogEvent(loginLog));
            AuthenticationContextHolder.clearContext();
        }
    }

    private Authentication phoneVerifyCodeLogin(LoginBody loginBody) throws RRException{
        if (StringUtils.isNotBlank(loginBody.getPhone())) {
            PhoneAuthenticationToken phoneAuthenticationToken = new PhoneAuthenticationToken(loginBody.getPhone(), loginBody.getCode());
            return  authenticationManager.authenticate(phoneAuthenticationToken);
        }
        return passwordLogin(loginBody);
    }

    private Authentication passwordLogin(LoginBody loginBody) throws RRException{
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginBody.getUsername(), loginBody.getPassword());
        AuthenticationContextHolder.setContext(authenticationToken);
        // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
        UsernamePasswordAuthenticationToken authToken =
                new UsernamePasswordAuthenticationToken(loginBody.getUsername(), loginBody.getPassword());
        return authenticationManager.authenticate(authToken);
    }

    /**
     * 登录验证
     *
     * @return 结果
     */
    @Override
    public String onceClickLogin(AppLoginBody loginBody) throws Exception {
        if (StringUtils.isBlank(loginBody.getChannel()) || StringUtils.isBlank(loginBody.getToken())) {
            throw new RRException(RRExceptionEnum.PARAM_ERROR);
        }
        if (!AppLoginType.containsKey(loginBody.getChannel())) {
            throw new RRException(RRExceptionEnum.LOGIN_TYPE_NOT_SUPPORT);
        }
        String phone = phoneParse(loginBody);
        if (!StringUtils.isNotBlank(phone)) {
            throw new RRException(RRExceptionEnum.APP_LOGIN_PHONE_EMPTY);
        }

        boolean loginSuccess = false;
        SysLoginLog loginLog = new SysLoginLog();
        // 用户验证
        try {
            OnceClickAuthenticationToken phoneAuthenticationToken = new OnceClickAuthenticationToken(phone);
            Authentication authentication = authenticationManager.authenticate(phoneAuthenticationToken);
            LoginUser loginUser = (LoginUser) authentication.getPrincipal();
            commonLoginHandler.statusVerify(loginUser);
            loginSuccess = true;
            return tokenService.createToken(loginUser);
        } catch (Exception e) {
            log.error("一键登录异常", e);
            if (e instanceof BadCredentialsException) {
                throw new RRException(RRExceptionEnum.ADMIN_ACCOUNT_ERROR);
            }
            if (e instanceof RRException) {
                throw new RRException(((RRException) e).getCode(), e.getMessage());
            } else {
                throw new ServiceException(e.getMessage());
            }
        } finally {
            loginLog.setOptionIp(IPUtils.getIpAddr(HttpContextUtils.getHttpServletRequest()));
            loginLog.setOptionName(loginSuccess ? "一键登录成功" : "一键登录失败");
            loginLog.setOptionTerminal(HttpContextUtils.getHttpServletRequest().getHeader("User-Agent"));
            loginLog.setUsername(loginBody.getChannel());
            loginLog.setOptionTime(new Date());
            SpringContextUtils.publishEvent(new LoginLogEvent(loginLog));
            AuthenticationContextHolder.clearContext();
        }
    }


    @Autowired
    private RedisCache redisCache;

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled) {
            String result = Optional.ofNullable(uuid).orElse("");
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + result;
            String captcha = redisCache.getString(verifyKey);
            if (captcha == null) {
                throw new RRException(RRExceptionEnum.APP_LOGIN_CODE_EMPTY);
            }
            redisCache.remove(verifyKey);
            if (!code.equalsIgnoreCase(captcha)) {
                throw new RRException(RRExceptionEnum.APP_LOGIN_CODE_ERROR);
            }
        }
    }


    /**
     * 登录前置校验
     *
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            throw new RRException(RRExceptionEnum.ADMIN_ACCOUNT_ERROR);
        }
//        // IP黑名单校验
//        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
//        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
//        {
//            throw new BlackListException();
//        }
    }

}
