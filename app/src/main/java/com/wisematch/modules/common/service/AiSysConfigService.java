package com.wisematch.modules.common.service;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.enums.DictTypeEnum;
import com.wisematch.modules.common.entity.AiSysConfig;

import java.util.Map;

public interface AiSysConfigService extends IService<AiSysConfig> {

    AiSysConfig getByKey(String name);


    /**
     * 获取验证码开关
     *
     * @return true开启，false关闭
     */
    boolean selectCaptchaEnabled();

    String selectConfigByKey(String configKey);

    JSONObject getInterviewConfig();

    Map<String,String> selectConfigByKeyMulti(DictTypeEnum[] dictTypeEnum);

}
