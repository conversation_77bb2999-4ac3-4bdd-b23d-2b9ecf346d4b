package com.wisematch.modules.chat.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.BeanCopyUtils;
import com.wisematch.common.utils.PageUtils;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiOrganization;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.mapper.AiOrganizationMapper;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.AiOrganizationService;
import com.wisematch.modules.common.handler.businessLicenseVerify.BusinessLicenseContext;
import com.wisematch.modules.common.handler.businessLicenseVerify.PreVerifyBusinessHandler;
import com.wisematch.modules.common.handler.businessLicenseVerify.RecognizeBusinessLicenseHandler;
import com.wisematch.modules.common.handler.businessLicenseVerify.VerifyBusinessLicenseHandler;
import com.wisematch.modules.invitation.entity.AiInvitation;
import com.wisematch.modules.invitation.service.AiInvitationService;
import com.wisematch.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;

import static com.wisematch.modules.chat.enums.WiserConstant.*;

@Service
@Slf4j
public class AiOrganizationServiceImpl extends ServiceImpl<AiOrganizationMapper, AiOrganization> implements AiOrganizationService {

    @Autowired
    RecognizeBusinessLicenseHandler recognizeBusinessLicenseHandler;

    @Autowired
    VerifyBusinessLicenseHandler verifyBusinessLicenseHandler;

    @Autowired
    SysUserService sysUserService;

    @Autowired
    PreVerifyBusinessHandler preVerifyBusinessHandler;
    @Autowired
    AiInvitationService aiInvitationService;

    @Override
    public void examCertification(String userId){
        if(!isCertified(userId)){
            throw new RRException(RRExceptionEnum.ORG_NOT_VERIFY);
        }
    }

    @Override
    public void save(BusinessLicenseContext businessLicenseContext) {

        String currentUserId = UserInfoUtils.getCurrentUserId();
        AiOrganization aiOrganization = this.getByUserId(currentUserId);
        AiOrganization generated = AiOrganization.generateAiOrganization(businessLicenseContext);
        if(null == aiOrganization){
            aiOrganization = generated;
        }
        String id = aiOrganization.getId();
        BeanUtils.copyProperties(generated, aiOrganization);
        aiOrganization.setId(id);
        aiOrganization.setUserId(currentUserId);
        this.saveOrUpdate(aiOrganization);
        sysUserService.updateOrgIdAndRedis(UserInfoUtils.getLoginUser(), aiOrganization);
    }

    @Override
    @Transactional
    public BizStatusVO certifiedEnterprise(CertifiedEnterpriseDTO org) throws ExecutionException, InterruptedException, JsonProcessingException {
        AiOrganization organization = this.getByUserId(UserInfoUtils.getCurrentUserId());
        if(organization==null){
            throw new RRException(RRExceptionEnum.ORG_NOT_FOUND);
        }
        organization.setUniqueSocialCreditCode(org.getUniqueSocialCreditCode());
        organization.setLegalPerson(org.getLegalPerson());
        organization.setBusinessAddress(org.getBusinessAddress());
        organization.setOrganizationName(org.getOrganizationName());
        organization.setLegalPersonIdCard(org.getLegalPersonIdCard());
        this.updateById(organization);

//        Integer certified = verifyBusinessLicenseHandler.verifyFourElements(org);
//        Integer bizStatus = certified==null?0:certified;
//        organization.setIsCertified(bizStatus);
//        this.updateById(organization);
//        return new BizStatusVO(bizStatus);
        //todo 后面改成下面的逻辑
        String invitationCode = org.getInvitationCode();
        AiInvitation inviteCode = aiInvitationService.getInviteCode(invitationCode, UserInfoUtils.getCurrentUserId());
        if(null == inviteCode){
            throw new RRException(RRExceptionEnum.INVITATION_VALID);
        }else {
            Integer certified = verifyBusinessLicenseHandler.verifyFourElements(org);
            Integer bizStatus = certified==null?0:certified;
            organization.setIsCertified(bizStatus);
            this.updateById(organization);
            aiInvitationService.consumeInvitation(inviteCode);
            return new BizStatusVO(bizStatus);
        }
    }

    @Override
    public void updateOrg(UpdateOrgDTO org) {
        AiOrganization organization = this.getByUserId(UserInfoUtils.getCurrentUserId());

        BeanCopyUtils.copyPropertiesIgnoreId(org, organization);
        if( WiserConstant.CERTIFIED.equals(organization.getIsCertified())){
            organization.setOrganizationName(null);
            organization.setUniqueSocialCreditCode(null);
        }

        this.updateById(organization);
    }

    @Override
    public boolean isCertified(String userId) {
        AiOrganization one = this.getOne(new LambdaQueryWrapper<AiOrganization>()
                .eq(AiOrganization::getUserId, userId)
                .eq(AiOrganization::getIsCertified, CERTIFIED)
                .eq(AiOrganization::getIsDel, NOT_DELETE));
        return !(null == one);
    }

    @Override
    public AiOrganization getByUserId(String userId) {
        return this.getOne(new LambdaQueryWrapper<AiOrganization>()
                .eq(AiOrganization::getUserId, userId)
                .eq(AiOrganization::getIsDel, NOT_DELETE));
    }

    @Override
    public AiOrganization saveObj(AiOrganizationVerifyDTO org) {
        AiOrganization aiOrganization = new AiOrganization();
        aiOrganization.setUserId(UserInfoUtils.getCurrentUserId());
        BeanUtils.copyProperties(org, aiOrganization);
        this.save(aiOrganization);
        return aiOrganization;
    }

    @Override
    public void updateObj(AiOrganizationVerifyDTO org) {
        AiOrganization aiOrganization = new AiOrganization();
        aiOrganization.setUpdateTime(new Date());
        BeanUtils.copyProperties(org, aiOrganization);
        this.updateById(aiOrganization);
    }

    @Override
    public Page<AiOrganization> pageQuery(AiOrganizationQueryDTO dto) {
        QueryWrapper<AiOrganization> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(dto.getOrganizationName())) {
            wrapper.lambda().like(AiOrganization::getOrganizationName, dto.getOrganizationName());
        }
        if (dto.getIsCertified() != null) {
            wrapper.lambda().eq(AiOrganization::getIsCertified, dto.getIsCertified());
        }
        if (dto.getIsDel() != null) {
            wrapper.lambda().eq(AiOrganization::getIsDel, dto.getIsDel());
        }
        if (dto.getIsOpen() != null) {
            wrapper.lambda().eq(AiOrganization::getIsOpen, dto.getIsOpen());
        }
        wrapper.lambda().eq(AiOrganization::getIsDel, NOT_DELETE);

        return PageUtils.doPage(this, dto, wrapper);
    }

    @Override
    public void batchLogicDelete(List<String> ids) {
        this.baseMapper.batchLogicDelete(ids);
    }

    @Override
    public License3MetaVerifyVO licenseMetaVerify(FileSrcDTO fileSrcDTO){
        BusinessLicenseContext businessLicenseContext  = new BusinessLicenseContext();
        businessLicenseContext.setFileSrc(fileSrcDTO.getFileSrc());
        try{
            recognizeBusinessLicenseHandler.recognizeBusinessLicense(businessLicenseContext);
        }catch (Exception e){
            throw new RRException(e.getMessage());
        }finally {
            this.save(businessLicenseContext);
        }
        License3MetaVerifyVO license3MetaVerifyVO = new License3MetaVerifyVO();
        BeanUtils.copyProperties(businessLicenseContext,license3MetaVerifyVO);
        return license3MetaVerifyVO;
    }

    @Override
    public void associate(AiOrganizationInsertDTO org) {
        AiOrganizationVerifyDTO aiOrganizationVerifyDTO = new AiOrganizationVerifyDTO();
        BeanUtils.copyProperties(org,aiOrganizationVerifyDTO);
        this.associate(aiOrganizationVerifyDTO);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void associate(AiOrganizationVerifyDTO org) {
//        String currentUserId = UserInfoUtils.getCurrentUserId();
//        AiOrganization aiOrganization = this.getByCreditCode(org.getUniqueSocialCreditCode());
        String currentUserId = UserInfoUtils.getCurrentUserId();
        AiOrganization aiOrganization = this.getByUserId(currentUserId);
        if(null == aiOrganization){
            aiOrganization = this.saveObj(org);
        }else {
            AiOrganizationVerifyDTO aiOrganizationVerifyDTO = new AiOrganizationVerifyDTO();
            BeanUtils.copyProperties(aiOrganization, aiOrganizationVerifyDTO);
            BeanUtils.copyProperties(org, aiOrganizationVerifyDTO);
            aiOrganizationVerifyDTO.setId(aiOrganization.getId());
            this.updateObj(aiOrganizationVerifyDTO);
        }
        org.setId(aiOrganization.getId());
        sysUserService.updateOrgIdWithRedis(UserInfoUtils.getLoginUser(), org);
    }

    @Override
    public AiOrganization getByCreditCode(String code) {
        return this.baseMapper.selectOne(new LambdaQueryWrapper<AiOrganization>().eq(AiOrganization::getUniqueSocialCreditCode, code));
    }

    public void verifyAssociate(AiOrganizationVerifyDTO org){
//        AiOrganization aiOrganization = this.getByCreditCode(org.getUniqueSocialCreditCode());
//        if(null != aiOrganization){
//            org.setId(aiOrganization.getId());
////            sysUserService.updateOrgIdWithRedis(UserInfoUtils.getLoginUser(), org);
//        }
        this.associate(org);
    }

    @Override
    public void changeOpenStatus(ChangeOpenStatusDTO dto) {
        this.update(new UpdateWrapper<AiOrganization>().lambda().eq(AiOrganization::getId,dto.getId())
                .set(AiOrganization::getIsOpen, dto.getIsOpen()).set(AiOrganization::getUpdateTime,new Date()));
    }

    @Override
    public void logicDelete(String id) {
        this.update(new UpdateWrapper<AiOrganization>().lambda().eq(AiOrganization::getId,id).set(AiOrganization::getIsDel,DELETED));
    }
    @Override
    public List<AiOrganization> listAll(AiOrganizationQueryDTO dto) {
        QueryWrapper<AiOrganization> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(dto.getOrganizationName())) {
            wrapper.lambda().like(AiOrganization::getOrganizationName, dto.getOrganizationName());
        }
        if (dto.getIsCertified() != null) {
            wrapper.lambda().eq(AiOrganization::getIsCertified, dto.getIsCertified());
        }
        if (dto.getIsDel() != null) {
            wrapper.lambda().eq(AiOrganization::getIsDel, dto.getIsDel());
        }
        if (dto.getIsOpen() != null) {
            wrapper.lambda().eq(AiOrganization::getIsOpen, dto.getIsOpen());
        }
        wrapper.lambda().eq(AiOrganization::getIsDel, NOT_DELETE);

        return this.list(wrapper);
    }
}

