package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.InterviewThreadPoolUtil;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.chat.entity.AiChatMemory;
import com.wisematch.modules.chat.enums.MsgRole;
import com.wisematch.modules.chat.mapper.AiChatMemoryMapper;
import com.wisematch.modules.chat.model.ShortMessage;
import com.wisematch.modules.chat.service.AiChatMemoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;


/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiChatMemoryServiceImpl extends ServiceImpl<AiChatMemoryMapper, AiChatMemory> implements AiChatMemoryService {

    @Override
    public void add(String conversationId, AiChatMemory aiChatMemory) {
        aiChatMemory.setTimestamp(new Date());
        this.baseMapper.insert(aiChatMemory);
    }

    @Override
    public void addUser(String conversationId, String message) {
        AiChatMemory lastedMemory = this.getLastedMemory(conversationId);
        if (lastedMemory != null && MsgRole.user.name().equals(lastedMemory.getType())) {
            String msg = lastedMemory.getContent() + message;
            lastedMemory.setContent(msg);
            lastedMemory.setTimestamp(new Date());
            this.baseMapper.updateById(lastedMemory);
            return;
        }
        AiChatMemory aiChatMemory = new AiChatMemory();
        aiChatMemory.setType(MsgRole.user.name());
        aiChatMemory.setContent(message);
        aiChatMemory.setTimestamp(new Date());
        aiChatMemory.setConversationId(conversationId);
        this.baseMapper.insert(aiChatMemory);
    }

    private AiChatMemory getLastedMemory(String conversationId) {
        QueryWrapper<AiChatMemory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiChatMemory::getConversationId, conversationId).orderByAsc(AiChatMemory::getId);
        List<AiChatMemory> list = this.baseMapper.selectList(queryWrapper);
        if (list != null && !list.isEmpty()) {
            return list.get(list.size() - 1);
        }
        return null;
    }

    @Override
    public void addUserAsync(String conversationId, String message) {
        InterviewThreadPoolUtil.supplyAsync(() -> {
            this.addUser(conversationId, message);
            return true;
        }).thenAccept(result -> {
            log.info("执行成功: {}", result);
        });
    }

    @Override
    public void addUserAsync(String conversationId, String message, Integer askIndex) {
        InterviewThreadPoolUtil.supplyAsync(() -> {
            this.addUser(conversationId, message, askIndex);
            return true;
        }).thenAccept(result -> {
            log.info("执行成功: {}", result);
        });
    }

    @Override
    public void addAssistantAsync(String conversationId, String message) {
        InterviewThreadPoolUtil.supplyAsync(() -> {
            this.addAssistant(conversationId, message);
            return true;
        }).thenAccept(result -> {
            log.info("执行成功: {}", result);
        });
    }

    @Override
    public void addAssistantAsync(String conversationId, String message, Integer askIndex) {
        InterviewThreadPoolUtil.supplyAsync(() -> {
            this.addAssistant(conversationId, message, askIndex);
            return true;
        }).thenAccept(result -> {
            log.info("执行成功: {}", result);
        });
    }

    @Override
    public void addAssistant(String conversationId, String message) {
        AiChatMemory lastedMemory = this.getLastedMemory(conversationId);
        if (lastedMemory != null && MsgRole.assistant.name().equals(lastedMemory.getType())) {
            String msg = lastedMemory.getContent() + message;
            lastedMemory.setContent(msg);
            lastedMemory.setTimestamp(new Date());
            this.baseMapper.updateById(lastedMemory);
            return;
        }
        AiChatMemory aiChatMemory = new AiChatMemory();
        aiChatMemory.setType(MsgRole.assistant.name());
        aiChatMemory.setContent(message);
        aiChatMemory.setTimestamp(new Date());
        aiChatMemory.setConversationId(conversationId);
        this.baseMapper.insert(aiChatMemory);
    }

    @Override
    public void addUser(String conversationId, String message, Integer askIndex) {
        AiChatMemory lastedMemory = this.getLastedMemory(conversationId);
        if (lastedMemory != null && MsgRole.user.name().equals(lastedMemory.getType())) {
            String msg = lastedMemory.getContent() + message;
            lastedMemory.setContent(msg);
            lastedMemory.setTimestamp(new Date());
            this.baseMapper.updateById(lastedMemory);
            return;
        }
        AiChatMemory aiChatMemory = new AiChatMemory();
        aiChatMemory.setType(MsgRole.user.name());
        aiChatMemory.setContent(message);
        aiChatMemory.setTimestamp(new Date());
        aiChatMemory.setConversationId(conversationId);
        aiChatMemory.setQuestionIndex(askIndex);
        this.baseMapper.insert(aiChatMemory);
    }

    @Override
    public void addAssistant(String conversationId, String message, Integer askIndex) {
        AiChatMemory lastedMemory = this.getLastedMemory(conversationId);
        if (lastedMemory != null && MsgRole.assistant.name().equals(lastedMemory.getType())) {
            String msg = lastedMemory.getContent() + message;
            lastedMemory.setContent(msg);
            lastedMemory.setTimestamp(new Date());
            this.baseMapper.updateById(lastedMemory);
            return;
        }
        AiChatMemory aiChatMemory = new AiChatMemory();
        aiChatMemory.setType(MsgRole.assistant.name());
        aiChatMemory.setContent(message);
        aiChatMemory.setTimestamp(new Date());
        aiChatMemory.setConversationId(conversationId);
        aiChatMemory.setQuestionIndex(askIndex);
        this.baseMapper.insert(aiChatMemory);
    }

    @Override
    public List<ShortMessage> getList(String conversationId) {
        QueryWrapper<AiChatMemory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiChatMemory::getConversationId, conversationId).orderByAsc(AiChatMemory::getId);
        List<AiChatMemory> list = this.baseMapper.selectList(queryWrapper);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        long startTime = list.get(0).getTimestamp().getTime();
        return list.stream().map(x -> {
            ShortMessage shortMessage = new ShortMessage();
            shortMessage.setRole(x.getType());
            shortMessage.setContent(x.getContent());
            shortMessage.setSecond((x.getTimestamp().getTime() - startTime) / 1000);
            return shortMessage;
        }).toList();
    }


}
