package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.*;
import com.wisematch.modules.chat.entity.AiJobTrain;
import com.wisematch.modules.chat.entity.AiUserPreferences;
import com.wisematch.modules.chat.enums.UserPreferenceType;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.mapper.AiJobTrainMapper;
import com.wisematch.modules.chat.model.AiJobTrainDTO;
import com.wisematch.modules.chat.model.AiUserPreferencesQueryDTO;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.PreferenceStatusDTO;
import com.wisematch.modules.chat.service.AiJobTrainService;
import com.wisematch.modules.chat.service.AiUserPreferencesService;
import com.wisematch.modules.chat.service.PreferenceShow;
import com.wisematch.modules.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.wisematch.modules.chat.enums.WiserConstant.DELETED;


/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiJobTrainServiceImpl extends ServiceImpl<AiJobTrainMapper, AiJobTrain> implements AiJobTrainService, PreferenceShow {

    @Autowired
    AiUserPreferencesService aiUserPreferencesService;
    @Override
    public List<CardInfo> getList() {
        List<AiJobTrain> aiJobTrains = this.list(new QueryWrapper<AiJobTrain>().lambda()
                        .eq(AiJobTrain::getStatus, WiserConstant.IN_USE)
                .eq(AiJobTrain::getIsDel, WiserConstant.NOT_DELETE)
                .eq(AiJobTrain::getIsVerify, WiserConstant.VERIFIED)
                .orderByDesc(AiJobTrain::getIsNewest,AiJobTrain::getIsHot,AiJobTrain::getPriority,AiJobTrain::getCreateTime));

        return BeanCopyUtils.copyList(aiJobTrains, CardInfo::jobTrainToCard);
    }

    @Override
    public void batchLogicDelete(List<String> ids) {
        this.baseMapper.batchLogicDelete(ids);
    }

    @Override
    public void logicDelete(String id) {
        this.update(new UpdateWrapper<AiJobTrain>().lambda().eq(AiJobTrain::getId,id).set(AiJobTrain::getIsDel,DELETED));
    }


    @Override
    public Page<CardInfo> pageQuery(AiJobTrainDTO aiJobTrain) {

        QueryWrapper<AiJobTrain> wrapper = new QueryWrapper<AiJobTrain>();

        if (StringUtils.isNotBlank(aiJobTrain.getPosition())) {
            wrapper.lambda().like(AiJobTrain::getPosition, aiJobTrain.getPosition());
        }
        if (StringUtils.isNotBlank(aiJobTrain.getLevel())) {
            wrapper.lambda().eq(AiJobTrain::getLevel, aiJobTrain.getLevel());
        }
        if (ObjectUtils.isNotNull(aiJobTrain.getStatus())) {
            wrapper.lambda().eq(AiJobTrain::getStatus, aiJobTrain.getStatus());
        }
        wrapper.lambda().eq(AiJobTrain::getIsDel, WiserConstant.NOT_DELETE);
        wrapper.lambda().eq(AiJobTrain::getStatus, WiserConstant.IN_USE);
        wrapper.lambda().eq(AiJobTrain::getIsVerify, WiserConstant.VERIFIED);
        Page<AiJobTrain> aiJobPositionPage = PageUtils.doPage(this, aiJobTrain, wrapper);

        return PageConvertUtils.convert(
                aiJobPositionPage,
                CardInfo::jobTrainToCard
        );

    }

    @Override
    public Page<CardInfo> storeList(AiUserPreferencesQueryDTO queryDTO) {
        queryDTO.setUserId(UserInfoUtils.getCurrentUserId());
        queryDTO.setCollect(WiserConstant.COLLECT);
        queryDTO.setBizSence(UserPreferenceType.INTERVIEW.name());
        Page<AiUserPreferences> aiUserPreferencesPage = aiUserPreferencesService.pageQuery(queryDTO);
        List<AiUserPreferences> aiUserPreferences = aiUserPreferencesPage.getRecords();
        List<String> ids = aiUserPreferences.stream().map(AiUserPreferences::getTargetId).toList();

        Page<CardInfo> cardInfoPage = new Page<>();
        if(!CollectionUtils.isEmpty(ids)){
            List<AiJobTrain> aiJobPositions = MybatisUtils.listByIds(this, ids);
            List<CardInfo> cardInfos = BeanCopyUtils.copyList(aiJobPositions, CardInfo::jobTrainToCard);
            cardInfos.forEach(c -> c.setExtra(JsonUtils.toJsonObject(new PreferenceStatusDTO(1))));
            BeanUtils.copyProperties(aiUserPreferencesPage,cardInfoPage);
            cardInfoPage.setRecords(cardInfos);
        }
        return cardInfoPage;
    }
}
