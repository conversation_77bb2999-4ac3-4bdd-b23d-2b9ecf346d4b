package com.wisematch.modules.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiChatMemory;
import com.wisematch.modules.chat.model.ShortMessage;

import java.util.List;


/**
 * AI智能体服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiChatMemoryService extends IService<AiChatMemory> {

    void add(String conversationId, AiChatMemory aiChatMemory);

    void addUser(String conversationId, String message);

    void addUser(String conversationId, String message, Integer askIndex);

    void addUserAsync(String conversationId, String message);

    void addUserAsync(String conversationId, String message, Integer askIndex);

    void addAssistant(String conversationId, String message);

    void addAssistant(String conversationId, String message, Integer askIndex);

    void addAssistantAsync(String conversationId, String message);

    void addAssistantAsync(String conversationId, String message, Integer askIndex);

    List<ShortMessage> getList(String conversationId);
}
