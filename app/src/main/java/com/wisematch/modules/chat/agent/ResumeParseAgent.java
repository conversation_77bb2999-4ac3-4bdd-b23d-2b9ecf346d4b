package com.wisematch.modules.chat.agent;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.docmind_api20220711.Client;
import com.aliyun.docmind_api20220711.models.SubmitDigitalDocStructureJobAdvanceRequest;
import com.aliyun.docmind_api20220711.models.SubmitDigitalDocStructureJobResponse;
import com.aliyun.sdk.service.ocr_api20210707.AsyncClient;
import com.aliyun.sdk.service.ocr_api20210707.models.RecognizeGeneralRequest;
import com.aliyun.sdk.service.ocr_api20210707.models.RecognizeGeneralResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.internal.LinkedTreeMap;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import com.wisematch.modules.chat.model.AliCommonConfigDTO;
import com.wisematch.modules.chat.model.DocMindConfigDTO;
import com.wisematch.modules.chat.model.RecognizeOcrDTO;
import com.wisematch.modules.chat.model.ResumeScoreDTO;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.common.utils.JsonUtils;
import darabonba.core.client.ClientOverrideConfiguration;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class ResumeParseAgent {

    @Autowired
    private AgentFacade agentFacade;

    @Autowired
    AiSysConfigService aiSysConfigService;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 简历评分
     * @param resumeJson
     * @return
     */
    public Integer resumeScore(String resumeJson) {
        String prompt = agentFacade.getPrompt(ViewerAgentConstant.RESUME_SCORE);
        prompt = prompt.replace("$resume", resumeJson);
        AgentContext context = new AgentContext();
        context.setAgentCode(ViewerAgentConstant.RESUME_SCORE);
        context.setPrompt(prompt);
        context.setUserMsg("帮我进行简历评分");
        String res = agentFacade.supply(context);

        try {
            ResumeScoreDTO dto = objectMapper.readValue(res, ResumeScoreDTO.class);
//            log.info("resume score:{}", dto);
            return dto.calculateFinalScore();
        } catch (JsonProcessingException e) {
            log.error("简历评分结果解析异常", e);
            return 0;
        }
    }





    /**
     * 简历JSON提取
     * @param resumeText
     * @return
     */
    public String resumeJsonExtract(String resumeText) {
        log.info("resume extract json start.");
        String prompt = agentFacade.getPrompt(ViewerAgentConstant.RESUME_PARSE_JSON);
        prompt = prompt.replace("$resume", resumeText);
        prompt = prompt.replace("$time", DateUtil.format(new Date(), DatePattern.CHINESE_DATE_FORMAT));
        AgentContext context = new AgentContext();
        context.setAgentCode(ViewerAgentConstant.RESUME_PARSE_JSON);
        context.setPrompt(prompt);
        context.setUserMsg("帮我解析一下简历");
        return agentFacade.supply(context);
    }

    public String docMindParse(String fileUrl) throws Exception{
        // 使用默认凭证初始化Credentials Client
        long start = System.currentTimeMillis();
        String aliVo = aiSysConfigService.selectConfigByKey("sys.agent.aliCommon");
        AliCommonConfigDTO aliCommonConfigDTO = JsonUtils.fromJson(aliVo, AliCommonConfigDTO.class);

        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(aliCommonConfigDTO.getKeyId())
                .accessKeySecret(aliCommonConfigDTO.getSecretKey())
                .build());

        String ocr = aiSysConfigService.selectConfigByKey("sys.agent.orc");
        RecognizeOcrDTO recognizeOcrDTO = JsonUtils.fromJson(ocr, RecognizeOcrDTO.class);
        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region(recognizeOcrDTO.getRegion())
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride(recognizeOcrDTO.getEndpointOverride())
                )
                .build();

        RecognizeGeneralRequest recognizeGeneralRequest = RecognizeGeneralRequest.builder()
                .url(fileUrl)
                .build();
        CompletableFuture<RecognizeGeneralResponse> response = client.recognizeGeneral(recognizeGeneralRequest);
        RecognizeGeneralResponse resp = response.get();
        client.close();

        JSONObject json = (JSONObject) JSON.toJSON(resp.getBody());
        JSONObject jsonData = JSONObject.parseObject((String) json.get("data"));
        log.info("docmind parse finished, 耗时：" + (System.currentTimeMillis() - start) + "ms");

        return replaceMask(jsonData.getString("content"));
    }


    public String docMindParse(InputStream inputStream, String fileName) throws Exception{

        // 使用默认凭证初始化Credentials Client
        long start = System.currentTimeMillis();

        String id2MetaVerifyDTO = aiSysConfigService.selectConfigByKey("sys.agent.docMind");
        DocMindConfigDTO docMindConfigDTO = JsonUtils.fromJson(id2MetaVerifyDTO, DocMindConfigDTO.class);

        Config config = new Config()
                // 通过credentials获取配置中的AccessKey ID
                .setAccessKeyId(docMindConfigDTO.getKeyId())
                // 通过credentials获取配置中的AccessKey Secret
                .setAccessKeySecret(docMindConfigDTO.getSecretKey());
        // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
        config.endpoint = docMindConfigDTO.getEndpoint();
        Client client = new Client(config);
        // 创建RuntimeObject实例并设置运行参数
        RuntimeOptions runtime = new RuntimeOptions();
        SubmitDigitalDocStructureJobAdvanceRequest request = new SubmitDigitalDocStructureJobAdvanceRequest();
        request.fileUrlObject = inputStream;
        request.fileName = fileName;
        request.revealMarkdown=true;
        // 发起请求并处理应答或异常。
        SubmitDigitalDocStructureJobResponse response = client.submitDigitalDocStructureJobAdvance(request, runtime);

        JSONObject json = (JSONObject) JSON.toJSON(response.getBody());
        log.info("docmind parse finished, 耗时：" + (System.currentTimeMillis() - start) + "ms");
        return resolveParse(json);
    }

    public String resolveParse(JSONObject json){

        LinkedTreeMap data = (LinkedTreeMap) json.get("data");
        ArrayList layouts = (ArrayList)data.get("layouts");
        String markdown_str = "";
        for (int i = 0; i < layouts.size(); i++) {
            LinkedTreeMap o = (LinkedTreeMap)layouts.get(i);
            if (o.get("type").equals("image")){
                continue;
            }
            if (o.get("type").equals("text")){
                String markdownContent = (String) o.get("markdownContent");
                markdown_str += markdownContent;
            }
        }
        return replaceMask(markdown_str);
    }

    private String replaceMask(String markdown_str){

        // ✅ 1. 去除多段乱码（空格分隔）整行，如：1b5 96a ...
        markdown_str = markdown_str.replaceAll("(?m)^([a-zA-Z0-9_-]{2,10}\\s+){6,}[a-zA-Z0-9_-]{2,10}\\s*$", "");

// ✅ 2. 去除一整行超长乱码（没有空格，长度大于30）
        markdown_str = markdown_str.replaceAll("(?m)^[a-zA-Z0-9_-]{20,}\\s*$", "");

// ✅ 3. 删除多余空行
        markdown_str = markdown_str.replaceAll("(?m)^\\s*$", "");

        return markdown_str;
    }

}
