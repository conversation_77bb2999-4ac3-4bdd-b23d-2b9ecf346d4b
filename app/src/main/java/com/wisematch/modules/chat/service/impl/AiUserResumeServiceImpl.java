package com.wisematch.modules.chat.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.agent.ResumeParseAgent;
import com.wisematch.modules.chat.entity.AiUserResume;
import com.wisematch.modules.chat.enums.UserResumeType;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.mapper.AiUserResumeMapper;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.AiUserResumeService;
import com.wisematch.modules.oss.entity.SysOss;
import com.wisematch.modules.oss.enums.FileType;
import com.wisematch.modules.oss.service.SysOssService;
import com.wisematch.modules.oss.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

import static com.wisematch.modules.chat.enums.WiserConstant.NOT_DELETE;
import static com.wisematch.modules.chat.enums.WiserConstant.OPEN;

@Service
@Slf4j
public class AiUserResumeServiceImpl extends ServiceImpl<AiUserResumeMapper, AiUserResume> implements AiUserResumeService {
    @Autowired
    SysOssService sysOssService;
    @Autowired
    ResumeParseAgent resumeParseAgent;

    @Override
    public void updateResume(AiUserResumeUpdateDTO aiUserResumeUpdateDTO) {
        AiUserResume aiUserResume = new AiUserResume();
        BeanUtils.copyProperties(aiUserResumeUpdateDTO, aiUserResume);
        this.baseMapper.updateById(aiUserResume);
    }

    @Override
    public Page<AiUserResume> pageQuery(AiUserResumeDTO aiUserResume) {
        Page<AiUserResume> page = new Page<>(aiUserResume.getPageNum(), aiUserResume.getPageSize());
        QueryWrapper<AiUserResume> wrapper = new QueryWrapper<AiUserResume>();

        wrapper.lambda().eq(AiUserResume::getUserId, UserInfoUtils.getCurrentUserId());
        if (StringUtils.isNotBlank(aiUserResume.getResumeText())) {
            wrapper.lambda().like(AiUserResume::getResumeText, aiUserResume.getResumeText());
        }
        if (StringUtils.isNotBlank(aiUserResume.getFileName())) {
            wrapper.lambda().like(AiUserResume::getFileName, aiUserResume.getFileName());
        }
        wrapper.lambda().eq(AiUserResume::getIsDel, NOT_DELETE).orderByDesc(AiUserResume::getUpdateTime);
        return this.page(page, wrapper);
    }

    @Override
    public List<AiUserResume> attachmentList(String userId) {
        QueryWrapper<AiUserResume> wrapper = new QueryWrapper<AiUserResume>();
        wrapper.lambda().eq(AiUserResume::getUserId, userId);
        wrapper.lambda().isNotNull(AiUserResume::getResumeUrl);
        wrapper.lambda().eq(AiUserResume::getIsDel, NOT_DELETE).orderByDesc(AiUserResume::getUpdateTime);
        return this.list(wrapper);
    }

    @Override
    public List<AiUserResume> online(String userId) {
        QueryWrapper<AiUserResume> wrapper = new QueryWrapper<AiUserResume>();
        wrapper.lambda().eq(AiUserResume::getUserId, userId);
        wrapper.lambda().isNotNull(AiUserResume::getResumeJson);
        wrapper.lambda().eq(AiUserResume::getIsDel, NOT_DELETE).orderByDesc(AiUserResume::getUpdateTime);
        return this.list(wrapper);
    }

    @Override
    public boolean onlineSave(UserResumeOnline online) {
        String jsonResume = JSONObject.toJSONString(online);
        if (StringUtils.isEmpty(online.getId())) {
            AiUserResume aiUserResume = new AiUserResume();
            aiUserResume.setUserId(UserInfoUtils.getCurrentUserId());
            aiUserResume.setResumeJson(jsonResume);
            aiUserResume.setFileName("在线简历");
            Integer completenessScore = UserResumeOnline.completenessScore(JSONObject.parseObject(jsonResume));
            aiUserResume.setCompletenessScore(completenessScore);
            this.baseMapper.insert(aiUserResume);
        } else {
            AiUserResume aiUserResume = this.getById(online.getId());
            aiUserResume.setResumeJson(jsonResume);
            Integer completenessScore = UserResumeOnline.completenessScore(JSONObject.parseObject(jsonResume));
            aiUserResume.setCompletenessScore(completenessScore);
            this.updateById(aiUserResume);
        }

//        //简历得分生成
//        ThreadPoolUtil.execute(() -> {
//            Integer resumeScore = resumeParseAgent.resumeScore(jsonResume);
//            AiUserResume aiUserResume = this.getById(id);
//            aiUserResume.setResumeScore(resumeScore);
//            this.updateById(aiUserResume);
//        });
        //TODO 提取求职意向

        return true;
    }

    @Override
    public UserResumeOnline onlineDetail(String id) {
        AiUserResume aiUserResume = this.getById(id);
        UserResumeOnline online = JSONObject.parseObject(aiUserResume.getResumeJson(), UserResumeOnline.class);
        online.setScore(aiUserResume.getCompletenessScore());
        return online;
    }

    /**
     * 获取有效的在线简历
     *
     * @param userId
     * @return
     */
    @Override
    public String getCurrentOpened(String userId) {
        QueryWrapper<AiUserResume> wrapper = new QueryWrapper<AiUserResume>();
        wrapper.lambda().eq(AiUserResume::getIsOpen, OPEN)
                .eq(AiUserResume::getUserId, userId)
                .eq(AiUserResume::getIsDel, NOT_DELETE);
        wrapper.lambda().orderByDesc(AiUserResume::getUpdateTime);
        List<AiUserResume> list = this.baseMapper.selectList(wrapper);
        for (AiUserResume resume : list) {
            if (!StringUtils.isBlank(resume.getResumeJson())) {
                return resume.getResumeJson();
            }
        }
        return "";
    }

    @Override
    public String getCurrentOpenedOrText(String userId) {
        QueryWrapper<AiUserResume> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(AiUserResume::getUserId, userId)
                .eq(AiUserResume::getIsOpen, OPEN)
                .eq(AiUserResume::getIsDel, NOT_DELETE);
        wrapper.lambda().orderByDesc(AiUserResume::getUpdateTime);
        List<AiUserResume> list = this.baseMapper.selectList(wrapper);

        for (AiUserResume resume : list) {
            if(JSONObject.isValid(resume.getResumeJson())){
                JSONObject resumeJSON = JSONObject.parseObject(resume.getResumeJson());
                if(!resumeJSON.isEmpty()){
                    return resume.getResumeJson();
                }
            }
        }
        for (AiUserResume resume : list) {
            String resumeText = resume.getResumeText();
            if (StringUtils.isBlank(resumeText) && StringUtils.isNotBlank(resumeText)) {
                return resumeText;
            }
        }
        return "";
    }

    /**
     * 获取在线简历
     *
     * @param userId
     * @return
     */
    private AiUserResume getOnlineResume(String userId) {
        QueryWrapper<AiUserResume> wrapper = new QueryWrapper<AiUserResume>();
        wrapper.lambda().eq(AiUserResume::getIsOpen, OPEN)
                .eq(AiUserResume::getUserId, userId)
                .eq(AiUserResume::getIsDel, NOT_DELETE);
        wrapper.lambda().orderByDesc(AiUserResume::getUpdateTime);
        List<AiUserResume> list = this.baseMapper.selectList(wrapper);
        if (list != null && !list.isEmpty()) {
            for (AiUserResume resume : list) {
                if (StringUtils.isNotBlank(resume.getResumeJson())) {
                    return resume;
                }
            }
        }
        return null;
    }

    @Override
    @Deprecated
    public String getOwnLasted(String userId) {
        QueryWrapper<AiUserResume> wrapper = new QueryWrapper<AiUserResume>();
        wrapper.lambda().eq(AiUserResume::getUserId, userId);
        wrapper.lambda().eq(AiUserResume::getIsDel, NOT_DELETE);
        wrapper.lambda().eq(AiUserResume::getIsOpen, 1);
        wrapper.lambda().orderByDesc(AiUserResume::getUpdateTime);
        List<AiUserResume> list = this.baseMapper.selectList(wrapper);
        for (AiUserResume resume : list) {
            if (!StringUtils.isEmpty(resume.getResumeText())) {
                return resume.getResumeText();
            }
        }
        return "";
    }

    public AiUserResume insertResume(SysOss sysOss) {
        AiUserResume aiUserResume = new AiUserResume();
        aiUserResume.setFileName(sysOss.getFileName());
        aiUserResume.setUserId(sysOss.getUserId());
        aiUserResume.setIsOpen(WiserConstant.OPEN);
        aiUserResume.setIsDel(WiserConstant.NOT_DELETE);
        // 3. 返回完整的OSS访问路径
        aiUserResume.setResumeUrl(sysOss.getUrl());
        aiUserResume.setIsAnalysis(UserResumeType.ANALYZING);
        this.save(aiUserResume);
        return aiUserResume;
    }

    @Override
    public AiUserResume uploadResume(MultipartFile file, String userId) {
        FileUtils.validateResume(file);

        long start = System.currentTimeMillis();
        SysOss sysOss = sysOssService.ossUpload(file, userId, FileType.RESUME.name());
        AiUserResume aiUserResume = insertResume(sysOss);
        String docResume;
        try {
            String fileName = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            if(FileUtils.examDocumentByName(fileName)){
                docResume = resumeParseAgent.docMindParse(inputStream ,fileName);
            }else if(FileUtils.examPictureByName(fileName)){
                docResume = resumeParseAgent.docMindParse(sysOss.getUrl());
            }else {
                docResume = "";
            }
            this.resumeUpdate(aiUserResume.getId(), docResume);
            log.info("简历文件解析耗时：{}",(System.currentTimeMillis()-start));
        } catch (Exception e) {
            log.error("简历文件解析异常", e);
            aiUserResume.setIsAnalysis(UserResumeType.ANALYZE_FAIL);
            this.updateById(aiUserResume);
            throw new RRException("解析简历异常，请重新上传");
        }

        try {
            this.resumeExtractJsonAsync(aiUserResume.getId());
        } catch (Exception e) {
            log.error("简历异步解析异常", e);
        }
        return aiUserResume;
    }

    @Override
    @Deprecated
    public AiUserResume uploadAndSaveResume(MultipartFile file, String userId) {

        SysOss sysOss = sysOssService.ossUpload(file, userId, FileType.RESUME.name());
        return insertResume(sysOss);
    }


    @Override
    public void batchLogicDelete(List<String> ids) {
        this.baseMapper.batchLogicDelete(ids);
    }

    @Override
    public void resumeUpdate(String resumeId, String docResume) {
        AiUserResume aiUserResume = this.getById(resumeId);
        aiUserResume.setResumeText(docResume);
        aiUserResume.setIsAnalysis(UserResumeType.ANALYZED);
        this.updateById(aiUserResume);
        log.info("简历解析更新完成");
    }


    /**
     * 在线简历自动提取
     * @param resumeId
     */
    @Override
    public void resumeExtractJsonAsync(String resumeId) {
        AiUserResume aiUserResume = this.getById(resumeId);
        if (aiUserResume.getIsAnalysis() != 2) {
            return;
        }
        //不存在在线简历时自动生成在线简历
        if (this.getOnlineResume(aiUserResume.getUserId()) == null) {
            log.info("在线简历开始提取...");
            AiUserResume userResume = new AiUserResume();
            userResume.setResumeJson(new JSONObject().toJSONString());
            userResume.setFileName("在线简历");
            userResume.setUserId(aiUserResume.getUserId());
            userResume.setCompletenessScore(0);
            userResume.setIsAnalysis(UserResumeType.ANALYZING);
            userResume.setResumeText(aiUserResume.getResumeText());
            this.save(userResume);
            ThreadPoolUtil.supplyAsync(() -> {
                String resume = "";
                try {
                    resume = resumeParseAgent.resumeJsonExtract(aiUserResume.getResumeText());
//                Integer resumeScore = resumeParseAgent.resumeScore(resume);
                    log.info("简历结构化解析：{}", resume);
                    log.info("在线简历状态：{}", JSONObject.toJSONString(userResume));
                    if (JSONUtil.isTypeJSON(resume)) {
                        JSONObject jsonObject = JSONObject.parseObject(resume);
                        PortraitBrief portraitBrief = JSONObject.parseObject(resume, PortraitBrief.class);
                        jsonObject.put("brief", portraitBrief);
                        userResume.setResumeJson(jsonObject.toJSONString());
                        Integer completenessScore = UserResumeOnline.completenessScore(jsonObject);
                        userResume.setCompletenessScore(completenessScore);
//                    userResume.setResumeScore(resumeScore);
                        userResume.setIsAnalysis(UserResumeType.ANALYZED);
                        log.info("在线简历提取完成：{}", resume);
                    }
                } catch (Exception e) {
                    log.error("在线简历提取异常", e);
                    userResume.setIsDel(1);
                    userResume.setIsAnalysis(UserResumeType.ANALYZE_FAIL);
                }finally {
                    this.updateById(userResume);
                }
                return true;
            });
        }
    }


    @Override
    public void changeStatus(ChangeResumeStatusDTO dto) {
//        if(WiserConstant.OPEN.intValue() == dto.getIsOpen()){
//            Long openNum = this.baseMapper.selectCount(new QueryWrapper<AiUserResume>().lambda()
//                    .eq(AiUserResume::getUserId, UserInfoUtils.getUserId()).eq(AiUserResume::getIsOpen, WiserConstant.OPEN));
//            if(openNum>=1){
//                throw new RRException(RRExceptionEnum.ONE_RESUME_RESTRICT.getCode(), RRExceptionEnum.ONE_RESUME_RESTRICT.getMessage());
//            }
//        }
        this.baseMapper.update(new UpdateWrapper<AiUserResume>().lambda()
                .eq(AiUserResume::getId, dto.getId()).set(AiUserResume::getIsOpen, dto.getIsOpen()));
    }

    @Override
    public void examResume(String userId) {
        String ownLasted = this.getCurrentOpened(userId);
        if (StringUtils.isBlank(ownLasted)) {
            throw new RRException(RRExceptionEnum.PLEASE_UPLOAD_RESUME);
        }
    }


    @Override
    public List<String> getBatchIdByStatus(List<Integer> isAnalysis) {
        return this.baseMapper.selectList(new QueryWrapper<AiUserResume>().lambda()
                        .in(AiUserResume::getIsAnalysis, isAnalysis).orderByAsc(AiUserResume::getCreateTime).eq(AiUserResume::getIsDel, NOT_DELETE)
                        .last("LIMIT " + WiserConstant.RESUME_ANALYSIS_BATCH_SIZE))
                .stream().map(AiUserResume::getId).toList();
    }
}
