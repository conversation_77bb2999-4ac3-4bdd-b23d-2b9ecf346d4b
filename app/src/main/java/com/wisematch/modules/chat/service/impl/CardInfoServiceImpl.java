package com.wisematch.modules.chat.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiViewPortrait;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.enums.InterviewStatus;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.model.AiJobPositionQueryDTO;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.service.AiJobPositionService;
import com.wisematch.modules.chat.service.AiViewPortraitService;
import com.wisematch.modules.chat.service.AiViewRecordService;
import com.wisematch.modules.chat.service.CardInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version CardInfoServiceImpl.java, v0.1 2025-07-26 16:23
 */
@Service
public class CardInfoServiceImpl implements CardInfoService {

    @Autowired
    private AiJobPositionService aiJobPositionService;
    @Autowired
    private AiViewRecordService aiViewRecordService;
    @Autowired
    private AiViewPortraitService aiTalentPortraitService;

    @Override
    public Page<CardInfo> positionCardInfo(AiJobPositionQueryDTO aiJobPosition) {
        aiJobPosition.setPositionStatus(WiserConstant.PUBLISHED);
        Page<CardInfo> page = aiJobPositionService.pageQueryFront(aiJobPosition);
        List<CardInfo> cardList = page.getRecords();

        if (cardList.isEmpty()) {
            return page;
        }

        String currentUserId = UserInfoUtils.getCurrentUserId();
        List<String> positionIds = cardList.stream()
                .map(CardInfo::getId)
                .collect(Collectors.toList());

        // 1. 批量查询面试记录，用Stream构建「岗位ID→面试记录」映射
        Map<String, AiViewRecord> recordMap = StringUtils.isBlank(currentUserId)
                ? Collections.emptyMap()
                : aiViewRecordService.list(new QueryWrapper<AiViewRecord>().lambda()
                        .eq(AiViewRecord::getUserId, currentUserId)
                        .in(AiViewRecord::getPositionId, positionIds).orderByAsc(AiViewRecord::getCreateTime))
                .stream()
                .collect(Collectors.toMap(
                        AiViewRecord::getPositionId,  // 键：岗位ID
                        record -> record,             // 值：面试记录
                        // 冲突时保留最新的
                        (oldVal, newVal) -> newVal
                ));

        // 2. 批量查询人才画像，用Stream构建「岗位ID→人才画像」映射
        Map<String, AiViewPortrait> portraitMap = this.getUserViewPortraits(currentUserId, positionIds);

        // 3. 修复核心问题：用Optional包装对象，安全调用ifPresent
        page.setRecords(cardList.stream()
                .peek(card -> {
                    // 处理面试记录：先通过Optional包装，避免NullPointerException
                    Optional.ofNullable(recordMap.get(card.getId()))
                            .ifPresent(record -> {  // 只有当record不为null时才执行
                                setCardInfo(card, record);
                            });
                    // 处理推荐状态（优先级更高，覆盖面试状态）
                    if (portraitMap.containsKey(card.getId())) {
                        card.setBizStatus("推荐中");
                    }
                })
                .toList()); // Stream最终收集为List

        return page;
    }

    private Map<String,AiViewPortrait> getUserViewPortraits(String userId,List<String> positionIds){

        LambdaQueryWrapper<AiViewPortrait> queryWrapper = new QueryWrapper<AiViewPortrait>().lambda()
                .eq(AiViewPortrait::getUserId, userId)
                .eq(AiViewPortrait::getVerifyStatus, 1)
                .orderByDesc(AiViewPortrait::getCreateTime);
        if (positionIds != null && !positionIds.isEmpty()) {
            queryWrapper.in(AiViewPortrait::getPositionId, positionIds);
        }

        return StringUtils.isBlank(userId)
                ? Collections.emptyMap()
                : aiTalentPortraitService.list(queryWrapper)
                .stream()
                .collect(Collectors.toMap(
                        AiViewPortrait::getPositionId,  // 键：岗位ID
                        portrait -> portrait,           // 值：人才画像
                        (oldVal, newVal) -> oldVal      // 冲突时保留旧值（按createTime排序后，旧值是最新的）
                ));
    }


    // 抽取的状态匹配方法，用Stream简化条件判断
    private void setCardInfo(CardInfo card, AiViewRecord record) {
        // 定义「面试状态→显示文本」的映射关系
        List<Map.Entry<String, String>> statusMappings = Arrays.asList(
                new AbstractMap.SimpleEntry<>(InterviewStatus.SUBMITTING.name(), "待提交"),
                new AbstractMap.SimpleEntry<>(InterviewStatus.SUBMITED.name(), "已提交"),
                new AbstractMap.SimpleEntry<>(InterviewStatus.INIT.name(), "待面试"),
                new AbstractMap.SimpleEntry<>(InterviewStatus.STOP.name(), "待面试"),
                new AbstractMap.SimpleEntry<>(InterviewStatus.CHATED.name(), "待面试")
        );

        // 用Stream匹配状态，找到则返回对应文本，否则返回空字符串
        String bizStatus = statusMappings.stream()
                .filter(mapping -> mapping.getKey().equals(record.getStatus()))
                .findFirst()
                .map(Map.Entry::getValue)
                .orElse("");

        card.setBizStatus(bizStatus);

        // 处理extra字段：若为null则新建JSONObject，再添加applyTime
        JSONObject extra = Optional.ofNullable(card.getExtra())
                .orElse(new JSONObject());
        extra.put("applyTime", DateUtil.format(record.getUpdateTime(), DatePattern.NORM_DATETIME_PATTERN));
        extra.put("roomId",record.getRoomId());
        extra.put("formType",record.getApplyType());
        card.setExtra(extra); // 注意：JSONObject是引用类型，需显式set回card
    }

    @Override
    public List<CardInfo> getApplyRecord(String userId) {
        Set<String> positionIds = new HashSet<>();
        Map<String, AiViewPortrait> portraitMap = this.getUserViewPortraits(userId, null);
        List<CardInfo> myRecordList = new ArrayList<>();
        this.aiViewRecordService.getViewRecord(userId, ApplyType.POSITION.name()).forEach(x -> {
            //同一个岗位面试记录去重，返回最新的面试记录
            if (!positionIds.contains(x.getPositionId())) {
                positionIds.add(x.getPositionId());
                CardInfo cardInfo = this.aiJobPositionService.getCardInfoById(x.getPositionId());
                setCardInfo(cardInfo, x);
                if (portraitMap.containsKey(x.getPositionId())) {
                    cardInfo.setBizStatus("推荐中");
                }
                myRecordList.add(cardInfo);
            }
        });
        return myRecordList;
    }
}
