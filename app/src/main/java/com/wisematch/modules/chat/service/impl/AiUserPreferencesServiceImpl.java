package com.wisematch.modules.chat.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.BeanCopyUtils;
import com.wisematch.common.utils.PageConvertUtils;
import com.wisematch.common.utils.PageUtils;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.*;
import com.wisematch.modules.chat.enums.CardType;
import com.wisematch.modules.chat.enums.UserPreferenceType;
import com.wisematch.modules.chat.factories.notifyMessage.JoinCadidateDTO;
import com.wisematch.modules.chat.factories.notifyMessage.NotifyMessageFactories;
import com.wisematch.modules.chat.mapper.AiUserPreferencesMapper;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.service.SysUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.wisematch.modules.chat.enums.UserPreferenceType.TALENT;
import static com.wisematch.modules.chat.enums.WiserConstant.COLLECT;

@Service
@Slf4j
public class AiUserPreferencesServiceImpl extends ServiceImpl<AiUserPreferencesMapper, AiUserPreferences> implements AiUserPreferencesService {

    @Autowired
    AiChatWiserMemoryService aiChatWiserMemoryService;

    @Autowired
    SysUserService sysUserService;
    @Autowired
    AiChatMatcherMemoryService aiChatMatcherMemoryService;
    @Autowired
    AiOrganizationService aiOrganizationService;
    @Autowired
    @Lazy
    AiJobPositionService aiJobPositionService;
    @Autowired
    @Lazy
    AiViewPortraitService aiViewPortraitService;

    @Autowired
    AiNotifyMessageService aiNotifyMessageService;

    @Resource
    private AiUserPreferencesMapper aiUserPreferencesMapper;

    @Override
    public Page<AiUserPreferences> pageQuery(AiUserPreferencesQueryDTO dto) {
        QueryWrapper<AiUserPreferences> wrapper = new QueryWrapper<>();

        wrapper.lambda().eq(AiUserPreferences::getUserId, UserInfoUtils.getCurrentUserId());

        if (StringUtils.isNotBlank(dto.getTargetId())) {
            wrapper.lambda().eq(AiUserPreferences::getTargetId, dto.getTargetId());
        }
        if (dto.getCollect() != null) {
            wrapper.lambda().eq(AiUserPreferences::getCollect, dto.getCollect());
        }
        if (dto.getThumbsUp() != null) {
            wrapper.lambda().eq(AiUserPreferences::getThumbsUp, dto.getThumbsUp());
        }
        if (StringUtils.isNotBlank(dto.getBizSence())) {
            wrapper.lambda().eq(AiUserPreferences::getBizSence, dto.getBizSence());
        }

        return PageUtils.doPage(this, dto, wrapper);
    }

    @Override
    public void logicDelete(String id) {
        this.update(new UpdateWrapper<AiUserPreferences>().lambda().eq(AiUserPreferences::getId,id));
    }

    @Override
    public void batchLogicDelete(List<String> ids) {
        this.baseMapper.batchLogicDelete(ids);
    }

    @Override
    public void saveOrUpdate(UserPreferenceVO userPreferenceVO) {

        if (UserPreferenceType.WISER_REPLY.name().equals(userPreferenceVO.getBizSence())) {
            aiChatWiserMemoryService.updateUserPreferences(userPreferenceVO);
            return;
        } else if (UserPreferenceType.MATCHER_REPLY.name().equals(userPreferenceVO.getBizSence())) {
            aiChatMatcherMemoryService.updateUserPreferences(userPreferenceVO);
            return;
        }else if(UserPreferenceType.TALENT.name().equals(userPreferenceVO.getBizSence())){

            aiOrganizationService.examCertification(UserInfoUtils.getCurrentUserId());

        }
        AiUserPreferences aiUserPreferences = this.getOne(userPreferenceVO.getUserId(), userPreferenceVO.getBizSence(), userPreferenceVO.getTargetId());
        if (aiUserPreferences == null) {
            aiUserPreferences = new AiUserPreferences();
        }
        BeanUtils.copyProperties(userPreferenceVO, aiUserPreferences);
        this.saveOrUpdate(aiUserPreferences);

//        AiViewPortrait aiViewPortrait = aiViewPortraitService.getById(aiUserPreferences.getTargetId());
//        AiJobPosition aiJobPosition = aiJobPositionService.getById(aiViewPortrait.getPositionId());
        this.notifyUser(aiUserPreferences, aiUserPreferences.getTargetId());
    }

    @Override
    public void notifyUser(AiUserPreferences aiUserPreferences, String targetId) {

        if (CardType.TALENT.name().equals(aiUserPreferences.getBizSence())
                && COLLECT.equals(aiUserPreferences.getCollect())) {

            AiViewPortrait aiViewPortrait = aiViewPortraitService.getById(targetId);
            AiJobPosition aiJobPosition = aiJobPositionService.getById(aiViewPortrait.getPositionId());
            SysUser sysUser = sysUserService.getById(aiViewPortrait.getUserId());

            AiOrganization aiOrganization = aiOrganizationService.getByUserId(UserInfoUtils.getCurrentUserId());

            AiNotifyMessage aiNotifyMessage = NotifyMessageFactories
                    .joinCandidateMsg(
                            new JoinCadidateDTO()
                                    .setCompanyId(aiUserPreferences.getUserId())
                                    .setCompanyName(Optional.ofNullable(aiOrganization).map(AiOrganization::getOrganizationName).orElse(""))
                                    .setUserId(aiViewPortrait.getUserId())
                                    .setSourceId(aiUserPreferences.getTargetId())

                    );
            aiNotifyMessage.setPosition(aiJobPosition.getPosition());
            aiNotifyMessage.setContent(sysUser.getName()+aiNotifyMessage.getContent());
            aiNotifyMessageService.notifyUser(aiNotifyMessage);
        }
    }

    @Override
    public AiUserPreferences viewObj(String userId, String targetId, String bizSence) {

        String currentUserId = UserInfoUtils.getCurrentUserId();
        AiUserPreferences one = this.getOne(currentUserId, CardType.TALENT.name(), targetId);
        if (one == null) {
            one = new AiUserPreferences(UserInfoUtils.getCurrentUserId(), targetId, CardType.TALENT.name());
            one.setViewStatus(1);
            this.save(one);
        }
        return one;
    }


    @Override
    public List<AiUserPreferences> getPreferenceList(List<String> targetIds, String userId, String bizSence) {
        return this.list(new LambdaQueryWrapper<AiUserPreferences>()
                .in(AiUserPreferences::getTargetId, targetIds)
                .eq(AiUserPreferences::getUserId, UserInfoUtils.getCurrentUserId())
                .eq(AiUserPreferences::getBizSence, TALENT));
    }


    @Override
    public AiUserPreferences getOne(String userId, String bizSence, String targetId) {
        return  this.baseMapper.selectOne(new QueryWrapper<AiUserPreferences>().lambda()
                .eq(AiUserPreferences::getUserId,userId)
                .eq(AiUserPreferences::getBizSence,bizSence)
                .eq(AiUserPreferences::getTargetId,targetId)
        );
    }

    @Override
    public Page<String> candidateUserId(CandidatesDTO candidatesDTO, String userid) {
        // 处理搜索条件分割
        List<String> splitSearch = Optional.ofNullable(candidatesDTO.getSearch())
                // 过滤空字符串（trim 处理避免纯空格场景）
                .filter(search -> StringUtils.isNotBlank(search.trim()))
                // 分割关键词（支持中英文逗号、竖线、空格及连续分隔符）
                .map(search -> search.split("[,，|｜\\s]+"))
                // 转换为流并过滤空元素
                .map(Arrays::stream)
                .map(stream -> stream.filter(StringUtils::isNotBlank))
                // 收集为不可变集合
                .map(Stream::toList)
                // 空值或空字符串时返回空集合
                .orElse(Collections.emptyList());
        Page<AiUserPreferences> page = new Page<>(candidatesDTO.getPageNum(), candidatesDTO.getPageSize());
        Page<AiUserPreferences> aiUserPreferencesPage = aiUserPreferencesMapper.selectPreferenceCandidate(page, candidatesDTO.getPositionId(), userid, splitSearch);
        return PageConvertUtils.convert(aiUserPreferencesPage, AiUserPreferences::getTargetId);
    }

    @Override
    public List<String> candidateUserId(String companyUserId) {
        QueryWrapper<AiUserPreferences> wrapper = new QueryWrapper<AiUserPreferences>();
        wrapper.lambda()
                .eq(AiUserPreferences::getUserId, companyUserId)
                .eq(AiUserPreferences::getBizSence, CardType.TALENT.name())
                .eq(AiUserPreferences::getCollect, COLLECT);

        return BeanCopyUtils.copyList(this.list(wrapper),AiUserPreferences::getTargetId);
    }


    @Override
    public ChatMessage wrapWiserChatMessage(ChatMessage chatMessage) {
//        AiUserPreferences aiUserPreference = this.getOne(UserInfoUtils.getUserId(), UserPreferenceType.WISER_REPLY.name(), chatMessage.getId());
//        if (aiUserPreference != null) {
//            chatMessage.setExtra(JsonUtils.toJsonObject(aiUserPreference.toPreferenceStatusDTO(aiUserPreference)));
//        }
        List<? extends CardInfo> wildcardList = chatMessage.getCardInfos();

        if (wildcardList != null) {
            List<CardInfo> cardInfos = new ArrayList<>(wildcardList);
            chatMessage.setCardInfos(cardInfoAddPreference(cardInfos));
        }
        return chatMessage;
    }

    @Override
    public List<CardInfo> cardInfoAddPreference(List<CardInfo> cardInfos) {
        return cardInfos.stream().peek(y -> {
            AiUserPreferences preferences = this.getOne(UserInfoUtils.getCurrentUserId(), y.getCardType(), y.getId());
            if (preferences != null) {
                y.setExtra(JsonUtils.toJsonObject(AiUserPreferences.toPreferenceStatusDTO(preferences)));
            }
        }).toList();
    }

    @Override
    public List<CardInfo> cardInfoAddPreference(String userId, List<CardInfo> cardInfos) {
        return cardInfos.stream().peek(y -> {
            AiUserPreferences preferences = this.getOne(userId, y.getCardType(), y.getId());
            log.info("userId:{},cardType:{},id:{}", userId, y.getCardType(), y.getId());
            if (preferences != null) {
                y.setExtra(JsonUtils.toJsonObject(AiUserPreferences.toPreferenceStatusDTO(preferences)));
            }
            log.info(JsonUtils.toJson(y));
        }).toList();
    }

    @Override
    public ChatMessage<CardInfoVO> wrapMatcherChatMessage(ChatMessage<CardInfoVO> chatMessage) {
//        AiUserPreferences aiUserPreference = this.getOne(UserInfoUtils.getUserId(), UserPreferenceType.MATCHER_REPLY.name(), chatMessage.getId());
//        if (aiUserPreference != null) {
//            chatMessage.setExtra(JsonUtils.toJsonObject(aiUserPreference.toPreferenceStatusDTO(aiUserPreference)));
//        }
        List<CardInfoVO> wildcardList = chatMessage.getCardInfos();
        if (wildcardList != null) {
            chatMessage.setCardInfos(wildcardList.stream().map(y -> {
                //TODO getCardType规范统一
                AiUserPreferences preferences = this.getOne(UserInfoUtils.getCurrentUserId(), y.getCardType(), y.getId());
                if (preferences != null) {
                    y.setExtra(JsonUtils.toJsonObject(AiUserPreferences.toPreferenceStatusDTO(preferences)));
                }
                return y;
            }).toList());
        }
        return chatMessage;
    }

}

