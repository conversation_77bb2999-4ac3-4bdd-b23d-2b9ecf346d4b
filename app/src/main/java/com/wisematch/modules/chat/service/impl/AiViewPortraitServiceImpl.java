package com.wisematch.modules.chat.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.common.utils.UserUtils;
import com.wisematch.modules.chat.agent.AgentContext;
import com.wisematch.modules.chat.agent.AgentFacade;
import com.wisematch.modules.chat.agent.AiPortraitAgentFacade;
import com.wisematch.modules.chat.convertor.PortraitRecommendDTOConvertor;
import com.wisematch.modules.chat.entity.*;
import com.wisematch.modules.chat.enums.*;
import com.wisematch.modules.chat.factories.notifyMessage.NotifyMessageFactories;
import com.wisematch.modules.chat.mapper.AiViewPortraitMapper;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.exam.service.AiAnswerQuestionService;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.LoginUser;
import com.wisematch.modules.sys.model.PrivacyProtectionChangeDTO;
import com.wisematch.modules.sys.service.SysUserService;
import com.wisematch.modules.sys.service.TokenService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.wisematch.modules.chat.enums.UserPreferenceType.TALENT;


/**
 * 系统用户
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiViewPortraitServiceImpl extends ServiceImpl<AiViewPortraitMapper, AiViewPortrait> implements AiViewPortraitService {
    @Autowired
    AiViewRecordService aiViewRecordService;
    @Autowired
    SysUserService sysUserService;
    @Autowired
    TokenService tokenService;

    @Resource
    private AiJobTrainService aiJobTrainService;
    @Resource
    private AiAnswerQuestionService aiAnswerQuestionService;

    @Autowired
    AiPortraitAgentFacade aiPortraitAgentFacade;

    @Autowired
    AiNotifyMessageService aiNotifyMessageService;

    @Autowired
    AiViewReportService aiViewReportService;

    @Autowired
    AiViewPrepareService aiViewPrepareService;

    @Autowired
    AiUserPreferencesService aiUserPreferencesService;
    @Autowired
    AiJobPositionService aiJobPositionService;

    @Resource
    private GeekRecommendMilvusService geekRecommendMilvusService;

    @Resource
    private AiAgentPoolService aiAgentPoolService;

    @Resource
    private AiChatMemoryService aiChatMemoryService;

    @Resource
    private AiJobExamineService aiJobExamineService;

    @Resource
    private ObjectMapper objectMapper;

    @Autowired
    AgentFacade agentFacade;

    @Override
    public CardInfo communicateCard(CommunicateCardDTO dto) {
        if (CardType.WISER.name().equals(dto.getSenderType())) {
            AiViewPortrait aiViewPortrait = this.getById(dto.getSourceId());
            return aiJobPositionService.getCardInfoById(aiViewPortrait.getPositionId());
        } else {
            return this.getPortraitCardById(dto.getSourceId());
        }
    }

    @Override
    public List<PortraitRecommendDTO> getChatRecommend(List<Message> messages, String jobExamineId, String userMsg) {
        // 1. 获取职位考核内容
        String jobExamine = Stream.of(jobExamineId)
                .filter(StringUtils::isNotEmpty)
                .map(id -> aiJobExamineService.getById(id))
                .map(AiJobExamine::getContent)
                .findFirst()
                .orElse("");

        ArrayList<CompletableFuture<?>> completableFutures = new ArrayList<>();

        // 2. 准备公共参数
        String messagesJson = JSONObject.toJSONString(messages);
        ExecutorService executor = ThreadPoolUtil.getInstance();

        try {
            // 存储每个维度的结果
            List<CompletableFuture<GeekIntent.InterviewReport>> interviewReportFutures = new ArrayList<>();
            List<CompletableFuture<GeekIntent.TalentPortraitDimension>> talentPortraitDimensionFutures = new ArrayList<>();
            List<CompletableFuture<GeekIntent.TalentPortraitEvaluation>> talentPortraitEvaluation = new ArrayList<>();


            JsonNode dimensions = objectMapper.readTree(jobExamine);
            for (JsonNode dimension : dimensions) {
                // 面试报告解析（每个维度一个）
                CompletableFuture<GeekIntent.InterviewReport> interviewReportFuture = CompletableFuture.supplyAsync(
                        () -> callAiAgent(MatcherAgentConstant.GEEK_CHAT_RECOMMEND_REPORT_PARSE, messagesJson, dimension.toString(), userMsg, new TypeReference<>() {
                        }),
                        executor);
                interviewReportFutures.add(interviewReportFuture);
                completableFutures.add(interviewReportFuture);

                // 人才画像维度解析（每个维度一个）
                CompletableFuture<GeekIntent.TalentPortraitDimension> talentPortraitDimensionFuture = CompletableFuture.supplyAsync(
                        () -> callAiAgent(MatcherAgentConstant.GEEK_CHAT_RECOMMEND_PORTRAIT_DIMENSION_PARSE, messagesJson, dimension.toString(), userMsg, new TypeReference<>() {
                        }),
                        executor);
                talentPortraitDimensionFutures.add(talentPortraitDimensionFuture);
                completableFutures.add(talentPortraitDimensionFuture);


                CompletableFuture<GeekIntent.TalentPortraitEvaluation> talentPortraitEvaluationFuture = CompletableFuture.supplyAsync(
                        () -> callAiAgent(MatcherAgentConstant.GEEK_CHAT_RECOMMEND_PORTRAIT_EVALUATION_PARSE, messagesJson, dimension.toString(), userMsg, new TypeReference<>() {
                        }),
                        executor);
                talentPortraitEvaluation.add(talentPortraitEvaluationFuture);
                completableFutures.add(talentPortraitEvaluationFuture);
            }


            // 工作经验解析
            CompletableFuture<GeekIntent.WorkExperience> workFuture = CompletableFuture.supplyAsync(
                    () -> callAiAgent(MatcherAgentConstant.GEEK_CHAT_RECOMMEND_WORK_PARSE, messagesJson, null, userMsg, new TypeReference<>() {
                    }),
                    executor);
            completableFutures.add(workFuture);

            // 教育经验解析
            CompletableFuture<GeekIntent.EduExperience> eduFuture = CompletableFuture.supplyAsync(
                    () -> callAiAgent(MatcherAgentConstant.GEEK_CHAT_RECOMMEND_EDU_PARSE, messagesJson, null, userMsg, new TypeReference<>() {
                    }),
                    executor);
            completableFutures.add(eduFuture);

            // 项目经验解析
            CompletableFuture<GeekIntent.ProjectExperience> projectFuture = CompletableFuture.supplyAsync(
                    () -> callAiAgent(MatcherAgentConstant.GEEK_CHAT_RECOMMEND_PROJ_PARSE, messagesJson, null, userMsg, new TypeReference<>() {
                    }),
                    executor);
            completableFutures.add(projectFuture);

            // 学校经验解析
            CompletableFuture<GeekIntent.SchoolExperience> schoolFuture = CompletableFuture.supplyAsync(
                    () -> callAiAgent(MatcherAgentConstant.GEEK_CHAT_RECOMMEND_SCHOOL_PARSE, messagesJson, null, userMsg, new TypeReference<>() {
                    }),
                    executor);
            completableFutures.add(schoolFuture);

            // 个人资料解析
            CompletableFuture<GeekIntent.Profile> profileFuture = CompletableFuture.supplyAsync(
                    () -> callAiAgent(MatcherAgentConstant.GEEK_CHAT_RECOMMEND_RESUME_PARSE, messagesJson, null, userMsg, new TypeReference<>() {
                    }),
                    executor);
            completableFutures.add(profileFuture);

            // 等待所有异步任务完成
            try {
                CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get(10, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.error("AI代理调用超时，jobExamineId: {}", jobExamineId, e);
            }

            // 聚合面试报告结果
            List<GeekIntent.InterviewReport> interviewReports = new ArrayList<>();
            for (CompletableFuture<GeekIntent.InterviewReport> future : interviewReportFutures) {
                GeekIntent.InterviewReport report = future.get();
                if (report != null && StringUtils.isNotEmpty(report.getDimensions())) {
                    interviewReports.add(report);
                }
            }

            // 聚合人才画像维度结果
            List<GeekIntent.TalentPortraitDimension> talentPortraitDimensions = new ArrayList<>();
            for (CompletableFuture<GeekIntent.TalentPortraitDimension> future : talentPortraitDimensionFutures) {
                GeekIntent.TalentPortraitDimension dimension = future.get();
                if (dimension != null && StringUtils.isNotEmpty(dimension.getDimensionName())) {
                    talentPortraitDimensions.add(dimension);
                }
            }

            List<String> summary = new ArrayList<>();
            List<String> highlights = new ArrayList<>();

            for (CompletableFuture<GeekIntent.TalentPortraitEvaluation> future : talentPortraitEvaluation) {
                GeekIntent.TalentPortraitEvaluation dimension = future.get();
                if (dimension != null) {
                    List<String> subHighlights = dimension.getHighlights();
                    List<String> subSummary = dimension.getSummary();
                    summary.addAll(subSummary);
                    highlights.addAll(subHighlights);
                }
            }

            GeekIntent.TalentPortraitEvaluation mergedEvaluation = new GeekIntent.TalentPortraitEvaluation();
            mergedEvaluation.setSummary(summary);
            mergedEvaluation.setHighlights(highlights);

            // 5. 组装GeekIntent对象
            GeekIntent geekIntent = new GeekIntent(
                    workFuture.get(),
                    eduFuture.get(),
                    projectFuture.get(),
                    schoolFuture.get(),
                    profileFuture.get(),
                    interviewReports, // 聚合后的面试报告列表
                    talentPortraitDimensions, // 聚合后的人才画像维度列表
                    mergedEvaluation // 合并后的人才画像评价
            );

            // 6. 后续逻辑保持不变
            List<String> roomIds = geekRecommendMilvusService.chatRecommend(geekIntent, jobExamineId);

            // 7. 查询基础数据：面试准备、人才画像
            List<AiViewPortrait> aiViewPortraits = getByRoomId(roomIds);
            List<AiViewPrepare> aiViewPrepares = aiViewPrepareService.getByRoomId(roomIds);

            // 8. 构建映射关系（按房间ID关联）
            Map<String, AiViewPrepare> roomPrepareMap = aiViewPrepares.stream()
                    .collect(Collectors.toMap(
                            AiViewPrepare::getRoomId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));

            // 9. 从AiViewPortrait入手构建结果
            return aiViewPortraits.stream()
                    .map(portrait -> PortraitRecommendDTOConvertor.convert(portrait, roomPrepareMap))
                    .toList();

        } catch (Exception e) {
            log.error("AiJobPositionServiceImpl#getChatRecommend执行异常，jobExamineId: {}", jobExamineId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 调用单个AI代理并返回对象
     */
    private <T> T callAiAgent(String agentCode, String messagesJson, String jobExamine, String userMsg, TypeReference<T> typeRef) {
        try {
            AiAgentPool aiAgentPool = aiAgentPoolService.getByCode(agentCode);
            String prompt = aiAgentPool.getPrompt();
            prompt = prompt.replace("$messages", messagesJson);
            if (StringUtils.isNotBlank(jobExamine)) {
                prompt = prompt.replace("$examine", jobExamine);
            }

            AgentContext agentContext = new AgentContext();
            agentContext.setAgentCode(agentCode);
            agentContext.setPrompt(prompt);
            agentContext.setUserMsg(userMsg);

            String reply = agentFacade.supply(agentContext);
            return objectMapper.readValue(reply, typeRef);
        } catch (Exception e) {
            log.error("调用AI代理失败，agentCode: {}", agentCode, e);
            return null;
        }
    }

    @Override
    public List<AiViewPortrait> getByRoomId(List<String> roomIds) {
        if (CollectionUtils.isEmpty(roomIds)) {
            return new ArrayList<>();
        }

        QueryWrapper<AiViewPortrait> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(AiViewPortrait::getRoomId, roomIds);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<AiViewPortrait> getByUserId(String userId) {
        QueryWrapper<AiViewPortrait> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiViewPortrait::getUserId, userId);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public AiViewPortrait getByRoomId(String roomId) {
        QueryWrapper<AiViewPortrait> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AiViewPortrait::getRoomId, roomId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<CardInfo> talentCenter(String position) {
        QueryWrapper<AiViewPortrait> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(position)) {
            queryWrapper.lambda().eq(AiViewPortrait::getPosition, position);
        }
        queryWrapper.lambda().eq(AiViewPortrait::getStatus, 1)
                .isNotNull(AiViewPortrait::getBrief)
                .isNotNull(AiViewPortrait::getPortrait)
                .last("LIMIT 1000")
                .orderByDesc(AiViewPortrait::getScore);
        List<AiViewPortrait> list = this.baseMapper.selectList(queryWrapper);
        return list.stream().map(this::portraitCardInfo).filter(x -> x.getId() != null).toList();
    }


    @Override
    public Page<CardInfoVO> talentCenterPage(TalentCenterPageDTO dto) {
        QueryWrapper<AiViewPortrait> queryWrapper = new QueryWrapper<>();

        String search = dto.getSearch();
        String expectedCity = dto.getExpectedCity();
        String positionId = dto.getPositionId();
        Integer pageNum = dto.getPageNum();
        Integer pageSize = dto.getPageSize();

        Page<AiViewPortrait> aiViewPortraitPage;

        if (StringUtils.isNotBlank(search) || StringUtils.isNotBlank(expectedCity) || StringUtils.isNotBlank(positionId)) {
            List<String> roomIds = geekRecommendMilvusService.query(search, expectedCity, positionId, dto.getPageNum(), dto.getPageSize());
            if (CollectionUtils.isEmpty(roomIds)) {
                return new Page<>(pageNum, pageSize);
            }
            List<AiViewPortrait> aiViewPortraits = list(new QueryWrapper<AiViewPortrait>().lambda().in(AiViewPortrait::getRoomId, roomIds));
            aiViewPortraitPage = new Page<>(pageNum, pageSize);
            aiViewPortraitPage.setRecords(aiViewPortraits);
        } else {
            queryWrapper.lambda().eq(AiViewPortrait::getStatus, 1).isNotNull(AiViewPortrait::getBrief)
                    .isNotNull(AiViewPortrait::getPortrait).ne(AiViewPortrait::getUserId, UserInfoUtils.getCurrentUserId())
                    .orderByDesc(AiViewPortrait::getScore).orderByDesc(AiViewPortrait::getId);

            Page<AiViewPortrait> page = new Page<>(pageNum, pageSize);
            aiViewPortraitPage = this.baseMapper.selectPage(page, queryWrapper);
        }

        List<AiViewPortrait> list = aiViewPortraitPage.getRecords();

        List<CardInfo> cardInfos = list.stream().map(this::portraitCardInfo).filter(x -> x.getId() != null).toList();
        // 1. 收集所有卡片的id作为targetId（去重处理）
        List<String> targetIds = cardInfos.stream()
                .map(CardInfo::getId)  // 获取CardInfo的id作为targetId
                .distinct()            // 去重减少查询量
                .toList();

        // 2. 一次性查询所有相关的偏好设置
        Map<String, AiUserPreferences> preferenceMap = new HashMap<>();
        if (!targetIds.isEmpty()) {

            List<AiUserPreferences> preferences = aiUserPreferencesService
                    .getPreferenceList(targetIds, UserInfoUtils.getCurrentUserId(), TALENT.name());

            // 3. 按targetId分组，每个targetId只保留第一个偏好设置
            for (AiUserPreferences pref : preferences) {
                String targetId = pref.getTargetId();
                // 仅当Map中没有该targetId时才存入，确保只保留第一个
                if (!preferenceMap.containsKey(targetId)) {
                    preferenceMap.put(targetId, pref);
                }
            }
        }

        // 4. 循环卡片列表，从Map中获取对应偏好设置并赋值
        cardInfos.forEach(card -> {
            AiUserPreferences preference = preferenceMap.get(card.getId());
            JSONObject extra = JsonUtils.toJsonObject(PreferenceStatusDTO.preferenceToDTO(preference));
            card.setExtra(extra);
        });

        Page<CardInfoVO> cardInfoPage = new Page<>(dto.getPageNum(), dto.getPageSize());
        BeanUtils.copyProperties(aiViewPortraitPage, cardInfoPage);

        cardInfoPage.setRecords(convert(cardInfos));
        return cardInfoPage;
    }

    @Override
    public List<CardInfoVO> convert(List<CardInfo> cardInfos) {

        if (CollectionUtils.isEmpty(cardInfos)) {
            return new ArrayList<>();
        }

        List<String> portraitIds = cardInfos.stream()
                .map(CardInfo::getId)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(portraitIds)) {
            return new ArrayList<>();
        }

        List<AiViewPortrait> aiViewPortraits = listByIds(portraitIds);
        List<String> userIds = aiViewPortraits.stream()
                .map(AiViewPortrait::getUserId)
                .distinct()
                .toList();

        Map<String, AiViewPortrait> portraitIdToPortraitMap = aiViewPortraits.stream()
                .collect(Collectors.toMap(AiViewPortrait::getId, Function.identity()));

        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        Map<String, SysUser> userIdToUserMap = sysUserService.listByIds(userIds).stream()
                .collect(Collectors.toMap(SysUser::getUserId, Function.identity()));

        return cardInfos.stream()
                .map(cardInfo -> {
                    CardInfoVO cardInfoVO = new CardInfoVO();
                    BeanUtils.copyProperties(cardInfo, cardInfoVO);

                    AiViewPortrait aiViewPortrait = portraitIdToPortraitMap.get(cardInfoVO.getId());

                    cardInfoVO.setPositionName(
                            Optional.ofNullable(aiViewPortrait)
                                    .map(AiViewPortrait::getPosition)
                                    .orElse("")
                    );

                    String userId = Optional.ofNullable(aiViewPortrait)
                            .map(AiViewPortrait::getUserId)
                            .orElse("");
                    SysUser sysUser = userIdToUserMap.get(userId);

                    String birthStr = Optional.ofNullable(sysUser)
                            .map(SysUser::getBirthDayStr)
                            .orElse("");
                    Integer age = UserUtils.calculateAge(birthStr);

                    Integer sex = Optional.ofNullable(sysUser)
                            .map(SysUser::getSex)
                            .orElse(null);
                    JSONObject extra = cardInfo.getExtra();
                    extra.put("sex", sex);

                    String keyLabel = cardInfoVO.getKeyLabel();
                    if (age != null) {
                        keyLabel = StringUtils.isEmpty(keyLabel) ? age + "岁" : age + "岁 | " +  keyLabel;
                    }
                    cardInfoVO.setKeyLabel(keyLabel);
                    return cardInfoVO;
                })
                .toList();
    }

    @Override
    public Page<CardInfoVO> orgCandidateCard(CandidatesDTO candidatesDTO) {
        Page<String> portraitIds = aiUserPreferencesService.candidateUserId(candidatesDTO, UserInfoUtils.getCurrentUserId());
        List<String> records = portraitIds.getRecords();
        log.info("ids: "+records);
        Stream<AiViewPortrait> aiChatInterviewStream = records.stream().map(this::getById)
                .filter(Objects::nonNull);

        List<CardInfo> list = aiChatInterviewStream.map(this::portraitCardInfo).toList();
        Page<CardInfoVO> page = new Page<>(candidatesDTO.getPageNum(), candidatesDTO.getPageSize());
        BeanUtils.copyProperties(portraitIds, page);
        page.setRecords(convert(list));
        return page;
    }

    @Override
    public AiViewPortrait getRecommend(String userId, String positionId) {
        QueryWrapper<AiViewPortrait> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(userId)) {
            queryWrapper.lambda().eq(AiViewPortrait::getUserId, userId);
        }
        if (StringUtils.isNotBlank(positionId)) {
            queryWrapper.lambda().eq(AiViewPortrait::getPositionId, positionId);
        }
        queryWrapper.lambda().eq(AiViewPortrait::getStatus, 1).orderByDesc(AiViewPortrait::getCreateTime);
        List<AiViewPortrait> list = this.baseMapper.selectList(queryWrapper);
        if (list == null || list.isEmpty()) {
            return null;
        } else {
            return list.get(0);
        }
    }


    @Override
    public List<TalentPortrait> orgCandidate(String companyUserId) {

        List<String> portraitIds = aiUserPreferencesService.candidateUserId(companyUserId);
        Stream<AiViewPortrait> aiChatInterviewStream = portraitIds.stream().map(x -> this.getById(x));
        return aiChatInterviewStream.map(x -> toPortrait(x)).toList();
    }

    /**
     * 封装人才简介
     *
     * @param aiViewPortrait
     * @return
     */
    private PortraitBrief toPortraitBrief(AiViewPortrait aiViewPortrait) {
        PortraitBrief brief = JSONObject.parseObject(aiViewPortrait.getPortrait(), PortraitBrief.class);
        brief.setUserId(aiViewPortrait.getUserId());
        brief.setRoomId(aiViewPortrait.getRoomId());
        brief.setId(aiViewPortrait.getId());
        brief.setPosition(aiViewPortrait.getPosition());
        return brief;
    }

    private boolean isEmpty(String str) {
        if (str == null) {
            return true;
        }
        return StringUtils.isEmpty(str);
    }

    @Override
    public CardInfo portraitCardInfo(AiViewPortrait aiViewPortrait) {

        if (null != aiViewPortrait && StringUtils.isNotBlank(aiViewPortrait.getBrief())) {
            return JsonUtils.fromJson(aiViewPortrait.getBrief(), CardInfo.class);
        }
        return toCardInfo(convert(aiViewPortrait));
    }

    private AiViewPortraitVO convert(AiViewPortrait aiViewPortrait) {
        AiViewPortraitVO aiViewPortraitVO = new AiViewPortraitVO();
        BeanUtils.copyProperties(aiViewPortrait, aiViewPortraitVO);
        String brief = aiViewPortrait.getBrief();
        if (StringUtils.isNotBlank(brief)) {
            CardInfo cardInfo = JSONObject.parseObject(brief, CardInfo.class);
            if (cardInfo != null) {
                aiViewPortraitVO.setLabels(cardInfo.getLabels());
            }
        }
        return aiViewPortraitVO;
    }

    @Override
    public void privacyProtectionChange(PrivacyProtectionChangeDTO params) {
        sysUserService.update(new UpdateWrapper<SysUser>().lambda()
                .eq(SysUser::getUserId, UserInfoUtils.getCurrentUserId())
                .set(SysUser::getPrivacyProtection, JsonUtils.toJson(params)));

        LoginUser loginUser = UserInfoUtils.getLoginUser();
        loginUser.setPrivacyProtection(JsonUtils.toJsonObject(params));
        tokenService.refreshToken(loginUser);

        List<AiViewPortrait> aiViewPortraits = this.getByUserId(UserInfoUtils.getCurrentUserId());
        aiViewPortraits.forEach(this::updatePortrait);
    }

    public CardInfo toCardInfo(AiViewPortraitVO aiViewPortrait) {
        PortraitBrief x;
        CardInfo cardInfo = new CardInfo();
        cardInfo.setId(aiViewPortrait.getId());
        try {
            x = JSONObject.parseObject(aiViewPortrait.getPortrait(), PortraitBrief.class);
        } catch (Exception e) {
            log.info("获取人才卡片异常", e);
            return cardInfo;
        }
        String userId = aiViewPortrait.getUserId();
        cardInfo.setUserId(userId);
        if (x != null) {
            cardInfo.setSummary(x.getComments());
        }
        cardInfo.setCardType(CardType.TALENT.name());

        String roomId = aiViewPortrait.getRoomId();
        AiViewPrepare aiViewPrepare = aiViewPrepareService.getByRoomId(roomId);

        if (aiViewPrepare == null) {
            log.warn("AiViewPrepare is null for roomId: {}", roomId);
            // 如果没有准备信息，返回空的CardInfo
            return cardInfo;
        }

        ResumeDTO resume = JSONObject.parseObject(aiViewPrepare.getResume(), ResumeDTO.class);

        if (resume == null) {
            log.warn("ResumeDTO is null for roomId: {}", roomId);
            // 如果没有简历信息，返回空的CardInfo
            return cardInfo;
        }

        JSONObject extra = new JSONObject();
        extra.put("label", "全职");
        extra.put("sex", resume.getSex());
        extra.put("position", aiViewPortrait.getPosition());

//        AiUserPreferences one = aiUserPreferencesService.getOne(userId, CardType.TALENT.name(), aiViewPortrait.getId());
//        JSONObject jsonObject = JsonUtils.toJsonObject(AiUserPreferences.toPreferenceStatusDTO(one));

//        extra.putAll(jsonObject);

        SysUser sysUser = sysUserService.getById(userId);
        cardInfo.setLabels(aiViewPortrait.getLabels());
        cardInfo = resume.addToCard(sysUser, cardInfo);

        cardInfo.setExtra(extra);
        return cardInfo;
    }


    private String getLabels(String historyChatMessage, String positionContent, String resume) {
        PortraitLabelInput portraitLabelInput = new PortraitLabelInput();
        portraitLabelInput.setHistory(historyChatMessage);
        portraitLabelInput.setJd(positionContent);
        portraitLabelInput.setResume(resume);
        return aiPortraitAgentFacade.getReportLabel(portraitLabelInput);

    }



    /**
     * 封装人才画像
     *
     * @param aiViewPortrait
     * @return
     */
    private TalentPortrait toPortrait(AiViewPortrait aiViewPortrait) {
        AiViewReport aiViewReport = this.aiViewReportService.getByRoomId(aiViewPortrait.getRoomId());
        AiViewPrepare aiViewPrepare = this.aiViewPrepareService.getByRoomId(aiViewPortrait.getRoomId());
        TalentPortrait talentPortrait = new TalentPortrait();
        PortraitBrief brief = new PortraitBrief();
        if (aiViewPrepare != null) {
            if (StringUtils.isNotBlank(aiViewPrepare.getResume()) && JSONUtil.isTypeJSON(aiViewPrepare.getResume())) {
                talentPortrait.setResume(JSONObject.parseObject(aiViewPrepare.getResume()));
                brief = JSONObject.parseObject(aiViewPrepare.getResume(), PortraitBrief.class);
            }
        }
        if (aiViewReport != null) {
            JSONObject report = JSONObject.parseObject(aiViewReport.getReport());
            report.put("videoUrl",aiViewReport.getChatUrl());
            talentPortrait.setReport(report);
        }
        talentPortrait.setPortrait(JSONObject.parseObject(aiViewPortrait.getPortrait()));
        brief.setUserId(aiViewPortrait.getUserId());
        brief.setRoomId(aiViewPortrait.getRoomId());
        if (talentPortrait.getPortrait() != null) {
            brief.setComments(talentPortrait.getPortrait().getString("comments"));
        }
        talentPortrait.setBrief(brief);
        talentPortrait.setCardInfo(this.portraitCardInfo(aiViewPortrait));
        return talentPortrait;
    }

    @Override
    public TalentPortrait getPortraitById(String id) {

        AiViewPortrait aiViewPortrait = this.getById(id);
        TalentPortrait portrait = toPortrait(aiViewPortrait);

        AiUserPreferences one = aiUserPreferencesService
                .viewObj(aiViewPortrait.getUserId(), aiViewPortrait.getId(), CardType.TALENT.name());
        portrait.getCardInfo().setExtra(JsonUtils.toJsonObject(AiUserPreferences.toPreferenceStatusDTO(one)));

        SysUser sysUser = sysUserService.getById(aiViewPortrait.getUserId());
        //隐私信息动态更新，视频播放/用户信息
        JSONObject report = portrait.getReport();
        this.setReportVideoPrivate(report, sysUser);
        portrait.setReport(report);

        PortraitBrief brief = TalentPortrait.getBriefIfAbsent(portrait);
        JSONObject extra = brief.getExtra();
        extra = aiNotifyMessageService.setBriefPrivate(extra, sysUser);
        brief.setExtra(extra);
        portrait.setBrief(brief);

        portrait.setMbtiInfo(aiAnswerQuestionService.getLatestReport(aiViewPortrait.getUserId()));
        return portrait;
    }


    public void setReportVideoPrivate(JSONObject report, SysUser sysUser) {
        if (null != report) {
            String privacyProtection = sysUser.getPrivacyProtection();
            PrivacyProtectionChangeDTO protection = PrivacyProtectionChangeDTO.getByString(privacyProtection);
            if (WiserConstant.PRIVATE_PROTECT.equals(protection.getVideoProtection())) {
                aiNotifyMessageService.setReportVideoPrivate(report,sysUser);
            } else {
                report.put("videoApplyStatus", NotifyMsgBizStatus.AGREE);
            }
        }
    }



    @Override
    public CardInfo getPortraitCardById(String id) {
        AiViewPortrait aiViewPortrait = this.getById(id);

        return this.toCardInfo(convert(aiViewPortrait));
    }


    @Override
    public boolean generatePortraitSchedule(String roomId) {
        AiViewPortrait aiViewPortrait = this.getByRoomId(roomId);
        if (aiViewPortrait != null) {
            removeById(aiViewPortrait.getId());
        }

        AiViewRecord record = aiViewRecordService.getByRoomId(roomId);
        String chatType = record.getChatType();

        if (ApplyType.TRAIN.name().equals(chatType)) {
            return true;
        }

        List<String> historyPositionPortraitIds = list(new QueryWrapper<AiViewPortrait>().lambda()
                .eq(AiViewPortrait::getUserId, record.getUserId())
                .eq(AiViewPortrait::getPositionId, record.getPositionId())
                .eq(AiViewPortrait::getStatus, 1)
                .eq(AiViewPortrait::getVerifyStatus, 1)).stream()
                .map(AiViewPortrait::getId)
                .toList();

        aiViewPortrait = createByRecord(record);
        try {
            aiViewPortrait.setPortrait(aiPortraitAgentFacade.getPortrait(aiViewPortrait.getRoomId()));
            AiViewPortraitVO aiViewPortraitVO = new AiViewPortraitVO();
            BeanUtils.copyProperties(aiViewPortrait, aiViewPortraitVO);
            String labels = generateLabels(aiViewPortrait);
            aiViewPortraitVO.setLabels(labels);
            CardInfo cardInfo = this.toCardInfo(aiViewPortraitVO);
            aiViewPortrait.setBrief(JsonUtils.toJson(cardInfo));
            if (aiViewPortrait.getBrief() != null && aiViewPortrait.getPortrait() != null) {
                aiViewPortrait.setStatus(1);
            } else {
                aiViewPortrait.setStatus(-1);
            }
        } catch (Exception e) {
            aiViewPortrait.setStatus(-1);
            log.error("generate portrait error", e);
        }

        Integer score = Optional.ofNullable(aiViewReportService.getByRoomId(roomId))
                .map(AiViewReport::getScore)
                .orElse(0);
        aiViewPortrait.setScore(score);

        if (score > 40) {
            aiViewPortrait.setVerifyStatus(1);
        } else {
            aiViewPortrait.setVerifyStatus(-1);
        }

        this.updatePortrait(aiViewPortrait);

        // 如果画像有效且存在多个历史画像，则删除旧画像并返回true
        if (aiViewPortrait.getStatus() == 1 && aiViewPortrait.getVerifyStatus() == 1 && historyPositionPortraitIds.size() > 1) {
            removeBatchByIds(historyPositionPortraitIds);
            return true;
        }

        return aiViewPortrait.getStatus() != -1;
    }



    @Override
    public TalentPortrait generatePortrait(String chatRoomId) {

        AiViewPortrait aiViewPortrait = this.getByRoomId(chatRoomId);
        if (Objects.nonNull(aiViewPortrait)) {
            if (aiViewPortrait.getStatus() == 1 || aiViewPortrait.getStatus() == -1) {
                return toPortrait(aiViewPortrait);
            }
        }

        AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(chatRoomId);
        //模拟面试不生成人才画像
        if (aiViewRecord == null || StringUtils.isNotBlank(aiViewRecord.getTrainId())) {
            return null;
        }

        List<String> historyPositionPortraitIds = list(new QueryWrapper<AiViewPortrait>().lambda()
                .eq(AiViewPortrait::getUserId, aiViewRecord.getUserId())
                .eq(AiViewPortrait::getPositionId, aiViewRecord.getPositionId())
                .eq(AiViewPortrait::getStatus, 1)
                .eq(AiViewPortrait::getVerifyStatus, 1)).stream()
                .map(AiViewPortrait::getId)
                .toList();

        aiViewPortrait = createByRecord(aiViewRecord);
        try {
            aiViewPortrait.setPortrait(aiPortraitAgentFacade.getPortrait(aiViewPortrait.getRoomId()));
            AiViewPortraitVO aiViewPortraitVO = new AiViewPortraitVO();
            BeanUtils.copyProperties(aiViewPortrait, aiViewPortraitVO);
            String labels = generateLabels(aiViewPortrait);
            aiViewPortraitVO.setLabels(labels);
            CardInfo cardInfo = this.toCardInfo(aiViewPortraitVO);
            aiViewPortrait.setBrief(JsonUtils.toJson(cardInfo));
            if (aiViewPortrait.getBrief() != null && aiViewPortrait.getPortrait() != null) {
                aiViewPortrait.setStatus(1);
            } else {
                aiViewPortrait.setStatus(-1);
            }
        } catch (Exception e) {
            aiViewPortrait.setStatus(-1);
            log.error("generate portrait error", e);
        }

        Integer score = Optional.ofNullable(aiViewReportService.getByRoomId(chatRoomId))
                .map(AiViewReport::getScore)
                .orElse(0);
        aiViewPortrait.setScore(score);

        //TODO 根据加入人才池规则入池
        if (score > 40) {
            aiViewPortrait.setVerifyStatus(1);
        } else {
            aiViewPortrait.setVerifyStatus(-1);
        }

        this.updatePortrait(aiViewPortrait);

        //如果成功生成新的人才画像，覆盖当前职位旧的人才画像
        if (aiViewPortrait.getStatus() == 1 && aiViewPortrait.getVerifyStatus() == 1 && historyPositionPortraitIds.size() > 1) {
            removeBatchByIds(historyPositionPortraitIds);
        }

        //TODO 幂等控制，已发送不重复发通知
        if (aiViewPortrait.getVerifyStatus() > 0) {
            //加入人才池
            AiNotifyMessage aiNotifyMessage = NotifyMessageFactories
                    .joinTalentPool(aiViewPortrait.getUserId(), aiViewPortrait.getId());
            aiNotifyMessage.setPosition(aiViewPortrait.getPosition());
            aiNotifyMessageService.save(aiNotifyMessage);
        }
        return toPortrait(aiViewPortrait);
    }

    @Override
    public AiJobPosition getPositionByPortrait(String portraitId) {
        AiViewPortrait aiViewPortrait = this.getById(portraitId);
        String positionId = aiViewPortrait.getPositionId();
        return aiJobPositionService.getById(positionId);
    }

    private String generateLabels(AiViewPortrait aiViewPortrait) {

        String chatRoomId = aiViewPortrait.getRoomId();
        AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(chatRoomId);

        String positionContent = null;
        String chatType = aiViewRecord.getChatType();
        if (ApplyType.POSITION.name().equals(chatType)) {
            String positionId = aiViewRecord.getPositionId();
            AiJobPosition position = aiJobPositionService.getById(positionId);
            if (position != null) {
                positionContent = position.getContent();
            }
        }

        if (ApplyType.TRAIN.name().equals(chatType)) {
            String trainId = aiViewRecord.getTrainId();
            AiJobTrain aiJobTrain = aiJobTrainService.getById(trainId);
            if (aiJobTrain != null) {
                positionContent = aiJobTrain.getContent();
            }
        }

        AiViewPrepare aiViewPrepare = aiViewPrepareService.getByRoomId(chatRoomId);
        String resume = Optional.ofNullable(aiViewPrepare).map(AiViewPrepare::getResume).orElse("");
        String historyChatMessage = JSONObject.toJSONString(aiChatMemoryService.getList(chatRoomId));
        return getLabels(historyChatMessage, positionContent, resume);
    }

    private AiViewPortrait createByRecord(AiViewRecord aiViewRecord) {
        AiViewPortrait aiViewPortrait = new AiViewPortrait();
        aiViewPortrait.setRoomId(aiViewRecord.getRoomId());
        aiViewPortrait.setPositionId(aiViewRecord.getPositionId());
        aiViewPortrait.setPosition(aiViewRecord.getChatName());
        aiViewPortrait.setUserId(aiViewRecord.getUserId());
        aiViewPortrait.setStatus(0);
        this.save(aiViewPortrait);
        return aiViewPortrait;
    }

    private void updatePortrait(AiViewPortrait aiViewPortrait) {
        try {
            CardInfo cardInfo = this.toCardInfo(convert(aiViewPortrait));
            aiViewPortrait.setBrief(JsonUtils.toJson(cardInfo));
            aiViewPortrait.setStatus(1);
        } catch (Exception e) {
            aiViewPortrait.setStatus(-1);
            log.error("generate portrait error", e);
        }
        this.baseMapper.updateById(aiViewPortrait);
    }

}
